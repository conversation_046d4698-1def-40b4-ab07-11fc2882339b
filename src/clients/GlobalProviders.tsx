'use client';

import { ReactNode } from 'react';

import { PROGRESS_BAR_CONFIG } from '@/constants/ui';
import { useAnalyticsPageTracking, useHeaderVisibility, useMobileMenuAutoClose } from '@/hooks';

import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar';
import { usePathname, useSearchParams } from 'next/navigation';

dayjs.extend(localeData);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(isBetween);
dayjs().localeData();

type Props = {
  header: ReactNode;
  footer: ReactNode;
  children: ReactNode;
};

const GlobalProviders = ({ children, header, footer }: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Custom hooks for better separation of concerns
  useAnalyticsPageTracking(pathname, searchParams);
  useMobileMenuAutoClose(pathname);

  const { hideHeader } = useHeaderVisibility(pathname);

  return (
    <>
      {!hideHeader && header}
      {children}
      {!hideHeader && footer}
      <ProgressBar {...PROGRESS_BAR_CONFIG} />
    </>
  );
};

export default GlobalProviders;
