'use client';

import { ReactNode, createContext, useCallback, useEffect, useState } from 'react';

import { getAddress } from '@/app/services/filters';

type Context = {
  addresses: string[];
  isFetching: boolean;
  fetchAddresses: (query?: string) => void;
};

export const RentalAddressesContext = createContext<Context>({} as Context);

type ContextProps = {};

const RentalAddressesContextContainer = ({
  children,
  endpointPath = 'listings-address',
}: {
  children: ReactNode;
  endpointPath?: string;
}) => {
  const [addresses, setAddresses] = useState<string[]>([]);
  const [isFetching, setIsFetching] = useState<boolean>(false);

  const fetchAddresses = useCallback(
    async (query = '') => {
      setIsFetching(true);
      const { addresses: data } = await getAddress<{ addresses: string[] }>(endpointPath, query);
      setAddresses(data);
      setIsFetching(false);
    },
    [endpointPath],
  );

  return (
    <RentalAddressesContext.Provider value={{ addresses, isFetching, fetchAddresses }}>
      {children}
    </RentalAddressesContext.Provider>
  );
};

export default RentalAddressesContextContainer;
