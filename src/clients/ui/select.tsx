import FormHelperText from '@/app/ui/form-helper-text';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

export interface SelectOption {
  id?: string;
  text: string;
  value: string;
}

type Props = {
  className?: string;
  name?: string;
  options: SelectOption[];
  label?: string;
  value?: string | number;
  onChange?: (value: any, name: string) => void;
  error?: boolean;
  helperText?: string;
};

const Select = ({
  className,
  label = '',
  options,
  name,
  onChange,
  error,
  helperText,
  value,
}: Props) => {
  return (
    <div className='relative'>
      <select
        className={twMerge(
          classNames(
            'select select-bordered font-normal leading-normal h-min min-h-min rounded focus:outline-0 min-w-[120px] p-4',
            error && 'border-red-main',
          ),
          className,
        )}
        value={value}
        onChange={(event) => onChange?.(event.target.value, name ?? '')}
      >
        <option value=''>{label}</option>
        {options.map((_option, index) => (
          <option key={index} value={_option.value}>
            {_option.text}
          </option>
        ))}
      </select>
      {helperText && (
        <div className='ml-2 absolute'>
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default Select;
