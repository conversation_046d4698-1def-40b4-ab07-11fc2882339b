import { KeyboardEvent, useCallback, useEffect, useRef } from 'react';

import { AutocompleteOption } from '.';

type Props = {
  options?: AutocompleteOption[];
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  onSelect?: (_o: AutocompleteOption) => void;
  onClosePopup: () => void;
  isFetchingData?: boolean;
  dontScroll?: boolean;
};

const AutocompleteItems = ({
  options = [],
  activeIndex,
  setActiveIndex,
  onSelect,
  onClosePopup,
  isFetchingData,
  dontScroll,
}: Props) => {
  const selectRef = useRef<null | HTMLUListElement>(null);
  const activeListItemRef = useRef<null | HTMLLIElement>(null);

  useEffect(() => {
    if (activeListItemRef.current && !dontScroll) {
      activeListItemRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [activeIndex, dontScroll]);
  return (
    <ul
      className='absolute z-[99999] top-[50px] left-0 right-0 border rounded bg-white max-h-[200px] overflow-y-auto'
      tabIndex={0}
      ref={selectRef}
    >
      {options.map((_option, index) => (
        <li
          ref={index === activeIndex ? activeListItemRef : null}
          className={`px-3 py-1 cursor-pointer ${
            index === activeIndex ? 'bg-primary-slate activeIndex' : 'hover:bg-primary-slate'
          }`}
          onClick={() => {
            onSelect?.(_option);
            onClosePopup();
          }}
          key={index}
        >
          {_option.label}
        </li>
      ))}
    </ul>
  );
};

export default AutocompleteItems;
