'use client';

import debounce from 'lodash/debounce';
import { KeyboardEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Nullable } from '@/types/common';

import Input from '../input';

import AutocompleteItems from './AutocompleteItems';

export type AutocompleteOption = { label: string; value: any };

type Props = {
  value?: Nullable<string>;
  options?: AutocompleteOption[];
  onSelect?: (_option: AutocompleteOption) => void;
  onChangeValue?: (text: string) => void;
  className?: string;
  isFetchingData?: boolean;
  fetchData?: (query?: string) => void;
  placeholder?: string;
  dontScroll?: boolean;
};

const Autocomplete = ({
  options = [],
  onSelect,
  value,
  onChangeValue,
  className = '',
  isFetchingData,
  fetchData,
  placeholder = '',
  dontScroll,
}: Props) => {
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [query, setQuery] = useState('');

  const fetchDataDebounced = debounce((val = '') => {
    fetchData?.(val);
  }, 300);

  const onChangeQuery = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      setQuery(value);
      fetchDataDebounced(value);
      if (!showPopup) {
        setShowPopup(true);
      }
    },
    [showPopup, fetchDataDebounced],
  );

  const onKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.code === 'Enter') {
        const selectedOption = options?.[activeIndex];
        onSelect?.(selectedOption);
        setShowPopup(false);
        setActiveIndex(0);
        e.preventDefault();
      }
      if (e.code === 'ArrowUp' && !isFetchingData) {
        setActiveIndex(activeIndex === 0 ? options.length - 1 : activeIndex - 1);
        return;
      }
      if (e.code === 'ArrowDown' && !isFetchingData) {
        console.log('ehere is Arrow Down');
        setActiveIndex(activeIndex === options.length - 1 ? 0 : activeIndex + 1);
        return;
      }
    },
    [isFetchingData, options, activeIndex, onSelect],
  );

  const onClickOutside = useCallback(() => {
    if (query !== value) {
      if (query.length === 0) {
        onChangeValue?.('');
      } else if (value && value.length > 0 && options.length > 0) {
        setQuery(value);
      } else if (query.length > 0) {
        onChangeValue?.(query);
      }
    }
    setShowPopup(false);
  }, [options.length, onChangeValue, query, value]);

  useEffect(() => {
    fetchData?.();
  }, [fetchData]);

  useEffect(() => {
    if (value) {
      setQuery(value);
    }
  }, [value]);

  useEffect(() => {
    setActiveIndex(0);
  }, [options]);

  return (
    <>
      <div className='relative w-full'>
        <Input
          className={`px-2.5 py-[14px] w-full text-sm ${className}`}
          value={query}
          onChange={onChangeQuery}
          onKeyDown={onKeyDown}
          placeholder={placeholder}
          onFocus={() => {
            if (query.length === 0) {
              setShowPopup(true);
              setActiveIndex(0);
            }
          }}
        />
        {showPopup && options.length > 0 && (
          <AutocompleteItems
            options={options}
            activeIndex={activeIndex}
            setActiveIndex={setActiveIndex}
            onSelect={onSelect}
            onClosePopup={() => setShowPopup(false)}
            isFetchingData={isFetchingData}
            dontScroll={dontScroll}
          />
        )}
      </div>
      {showPopup && <div className='drawer-overlay absolute inset-0' onClick={onClickOutside} />}
    </>
  );
};

export default Autocomplete;
