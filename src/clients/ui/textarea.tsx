import FormHelperText from '@/app/ui/form-helper-text';
import InputLabel from '@/app/ui/input-label';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
} & React.HTMLProps<HTMLTextAreaElement>;

const Textarea = ({
  label = '',
  required,
  error,
  helperText,
  className,
  placeholder,
  ...props
}: Props) => {
  return (
    <div className='relative'>
      {!!label && (
        <InputLabel error={error}>
          {label}
          {required && <span className='required-mark'>*</span>}
        </InputLabel>
      )}
      <textarea
        {...props}
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'input input-bordered h-min min-h-min rounded focus:outline-none p-4',
            error && 'border-red-main',
          ),
          className,
        )}
      />
      {helperText && (
        <div className='ml-2 absolute r'>
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default Textarea;
