'use client';

import { cva } from 'class-variance-authority';
import { twMerge } from 'tailwind-merge';

type Props = {
  title?: string;
  isSubmit?: boolean;
  intent?: 'primary' | 'secondary' | 'outline' | 'disabled';
  children?: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
} & React.HTMLProps<HTMLButtonElement>;

const button = cva('button', {
  variants: {
    intent: {
      primary: ['bg-navy-blue', 'text-white', 'border-transparent', 'enabled:hover:bg-[#4c737f]'],
      secondary: ['bg-white', 'text-[#0E1717]', 'border', 'hover:bg-gray-100'],
      disabled: ['bg-gray-300 hover:bg-gray-400'],
      outline: ['bg-transparent', 'border', 'hover:border-opacity-60'],
    },
    size: {
      small: ['text-sm', 'py-1', 'px-2', 'min-h-min', 'h-9'],
      medium: ['text-base', 'py-2', 'px-4', 'font-medium'],
      large: ['text-base', 'py-3', 'px-4', 'font-bold'],
    },
  },
  compoundVariants: [{ intent: 'primary', size: 'large', class: '' }],
  defaultVariants: {
    intent: 'primary',
    size: 'medium',
  },
});

const Button = ({
  title,
  onClick,
  isSubmit,
  intent,
  className = '',
  children,
  size,
  icon,
  ...rest
}: Props) => (
  <button
    {...rest}
    type={isSubmit ? 'submit' : 'button'}
    className={button({
      intent,
      size,
      className: twMerge(
        'flex items-center justify-center rounded focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed',
        className,
      ),
    })}
    onClick={onClick}
  >
    {icon && <span>{icon}</span>}
    {title ?? children}
  </button>
);

export default Button;
