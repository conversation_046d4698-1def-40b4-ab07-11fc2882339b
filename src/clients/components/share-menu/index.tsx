'use client';

import React, { useState } from 'react';

import { ShareIcon } from '@heroicons/react/24/outline';

import Dropdown from '@/app/ui/dropdown';

import classNames from 'classnames';
import dynamic from 'next/dynamic';

const ShareComponent = dynamic(() => import('./ShareComponent'), {
  ssr: false,
});

export enum SharePlatformType {
  Twittter = 'Twitter',
  FB = 'FB',
  Linkedin = 'Linkedin',
  Email = 'Email',
  SMS = 'SMS',
  Copy_text = 'Copy text',
}

interface Props {
  title?: string;
  shareURL: string;
  shareText?: string;
}

const ShareMenu = ({ title, shareURL, shareText }: Props) => {
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  return (
    <div onClick={() => setDropdownOpen(true)}>
      <Dropdown
        className='dropdown-end dropdown-bottom'
        labelClassName='font-normal'
        title={
          <div className='flex cursor-pointer items-center'>
            <ShareIcon className='mr-2 h-4 w-4' />
            <span className='text-sm leading-[130%] underline'>Share</span>
          </div>
        }
        contentClassName={classNames(
          'min-w-min mt-2 rounded border transition-all shadow text-default-font p-0 visible',
          dropdownOpen ? '!visible' : '!invisible',
        )}
      >
        {dropdownOpen && (
          <ShareComponent
            onClose={() => setDropdownOpen(false)}
            title={title}
            shareURL={shareURL}
            shareText={shareText}
          />
        )}
      </Dropdown>
    </div>
  );
};

export default ShareMenu;
