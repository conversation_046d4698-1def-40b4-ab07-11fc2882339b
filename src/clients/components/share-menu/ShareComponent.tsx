'use client';

import React from 'react';

import Image from 'next/image';

import { SharePlatformType } from '.';

type Props = {
  title?: string;
  shareURL: string;
  shareText?: string;
  onClose: () => void;
};

const ShareComponent = ({ title, shareURL, shareText = '', onClose }: Props) => {
  const handleShare = (event: React.MouseEvent<HTMLElement, MouseEvent>, type: string) => {
    event.stopPropagation();
    event.preventDefault();
    onClose();
    switch (type) {
      case SharePlatformType.Twittter:
        window.open(
          `https://twitter.com/intent/tweet?url=${encodeURIComponent(
            shareText,
          )} ${encodeURIComponent(shareURL)}`,
          '_blank',
        );
        break;
      case SharePlatformType.FB:
        window.open(
          `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
            shareURL,
          )}&quote=${encodeURIComponent(shareText)}`,
          '_blank',
        );
        break;
      case SharePlatformType.Linkedin:
        window.open(
          `https://www.linkedin.com/shareArticle?url=${encodeURIComponent(shareURL)}`,
          '_blank',
        );
        break;
      case SharePlatformType.Email:
        window.open(
          `mailto:?subject=${encodeURIComponent(shareText)}&body=${encodeURIComponent(shareURL)}`,
          '_blank',
        );
        break;
      case SharePlatformType.SMS:
        window.open(
          `sms:?&body=${encodeURIComponent(shareText)}    ${encodeURIComponent(shareURL)}`,
          '_blank',
        );
        break;
      case SharePlatformType.Copy_text:
        navigator.clipboard.writeText(shareURL);
    }
  };
  return (
    <div className='py-2 px-4'>
      {title && <p className='text-sm font-medium mb-4'>{title}</p>}
      <div className='flex items-center gap-2'>
        <div
          onClick={(e) => handleShare(e, SharePlatformType.FB)}
          className='p-1 cursor-pointer relative w-10 h-10 rounded-full hover:bg-red-100 overflow-hidden'
        >
          <Image fill src={`/images/icons/facebook.svg`} alt='facebook icon' />
        </div>
        <div
          onClick={(e) => handleShare(e, SharePlatformType.Twittter)}
          className='p-1 cursor-pointer relative w-10 h-10 flex items-center rounded-full hover:bg-red-100'
        >
          <Image fill src={`/images/icons/twitter.svg`} alt='twitter icon' />
        </div>
        <div
          onClick={(e) => handleShare(e, SharePlatformType.SMS)}
          className='p-1 cursor-pointer relative w-10 h-10 flex items-center rounded-full hover:bg-red-100'
        >
          <Image fill src={`/images/icons/sms.svg`} alt='sms icon' />
        </div>
        <div
          onClick={(e) => handleShare(e, SharePlatformType.FB)}
          className='p-1 cursor-pointer relative w-10 h-10 flex items-center rounded-full hover:bg-red-100'
        >
          <Image fill src={`/images/icons/linkedin.svg`} alt='linkedin icon' />
        </div>
        <div
          onClick={(e) => handleShare(e, SharePlatformType.Email)}
          className='p-1 cursor-pointer relative w-10 h-10 flex items-center rounded-full hover:bg-red-100'
        >
          <Image fill src={`/images/icons/mail.svg`} alt='mail icon' />
        </div>
        <div
          onClick={(e) => handleShare(e, SharePlatformType.Copy_text)}
          className='p-1 cursor-pointer relative w-10 h-10 flex items-center rounded-full hover:bg-red-100'
        >
          <Image fill src={`/images/icons/copy.svg`} alt='copy icon' />
        </div>
      </div>
    </div>
  );
};

export default ShareComponent;
