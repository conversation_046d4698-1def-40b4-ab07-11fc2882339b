import Image from 'next/image';

type IconProps = {
  icon: string;
  size?: 'sm' | 'md';
  alt?: string;
  height?: number;
  width?: number;
};

export const Icon = ({ icon, size = 'sm', height, width, alt }: IconProps) => {
  const sizes = {
    sm: 17,
    md: 24,
  };

  return (
    <>
      {icon ? (
        <Image
          height={height ?? sizes[size]}
          width={width ?? sizes[size]}
          src={`/images/icons/${icon}.svg`}
          alt={alt ?? `${icon} icon`}
        />
      ) : null}
    </>
  );
};
