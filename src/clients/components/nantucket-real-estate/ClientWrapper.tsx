'use client';

import { ReactNode, useEffect } from 'react';

import { RealEstatePropertyProps } from '@/app/components/real-estate/PropertyCard';
import { SegmentEvents } from '@/types/analytics';

type Props = {
  children: ReactNode;
  realEstates: RealEstatePropertyProps[];
  searchParams: { offset: number };
};

const ClientWrapper = ({ children, realEstates, searchParams }: Props) => {
  useEffect(() => {
    window?.analytics?.track(SegmentEvents.PRODUCT_LIST_VIEWED, {
      action: 'Viewed',
      category: 'Product List',
      object: 'Product List',
      list_id: realEstates.map((_listing) => _listing.link_id),
      products: realEstates.map((_listing, index) => ({
        product_id: _listing.link_id,
        name: _listing.Address,
        category: _listing.MLSAreaMajor,
        position: index + 1,
        price: _listing.ListPrice,
        quantity: '1',
        brand: _listing.ListAgentFullName ?? '',
        image_url: _listing?.link_images?.[0]?.url ?? '',
      })),
    });
  }, [realEstates]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [searchParams]);

  return children;
};

export default ClientWrapper;
