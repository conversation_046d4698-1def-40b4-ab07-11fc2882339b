'use client';

import { useEffect } from 'react';

import { IRealEstateDetail } from '@/app/nantucket-real-estate/[...slug]/page';
import { SegmentEvents } from '@/types/analytics';

const RealEstateWrapper = ({
  children,
  realEstateDetails,
}: {
  children: React.ReactNode;
  realEstateDetails: IRealEstateDetail;
}) => {
  useEffect(() => {
    window?.analytics?.track(SegmentEvents.LISTING_DETAIL_VIEWED, {
      listing_id: realEstateDetails.link_id,
      listing_name: realEstateDetails.Address,
      listing_number_of_bedrooms: realEstateDetails.BedroomsTotal,
      listing_type: 'Real Estate',
      price: realEstateDetails.ListPrice,
      bathrooms: realEstateDetails.BathroomsTotalDecimal,
      bedrooms: realEstateDetails.BedroomsTotal,
      capacity: realEstateDetails.RoomsTotal,
      neighborhood: realEstateDetails.MLSAreaMajor,
      region: 'Massachusetts',
      city: 'Nantucket',
      country: 'United States',
      url: document.URL,
      referrer: document.referrer,
    });
  }, [realEstateDetails]);
  return children;
};

export default RealEstateWrapper;
