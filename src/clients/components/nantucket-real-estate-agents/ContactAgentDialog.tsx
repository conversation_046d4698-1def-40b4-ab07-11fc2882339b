'use client';

import { useCallback, useState } from 'react';

import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

import { contactAgentPM } from '@/app/services/agents';
import InputLabel from '@/app/ui/input-label';
import { ContactMethod } from '@/clients/components/rental-listing-details/contact-agent-form';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import { IAgent } from '@/types/agents';
import { ProgressStatus } from '@/types/common';

import classNames from 'classnames';

type Props = {
  agent: IAgent;
  open: boolean;
  onClose: () => void;
};

export type FormValues = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message: string;
};

const ContactAgentDialog = ({ agent, open, onClose }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const [contactMethod, setContactMethod] = useState<ContactMethod>(ContactMethod.PHONE);
  const { formState, pristine, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      message: '',
    },
    {
      firstName: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Firstname is required.`;
        }
      },
      lastName: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Lastname is required.`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Email is required.`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Phone number is required.`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      message: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Message is required.`;
        }
      },
    },
  );

  const onSubmit = useCallback(
    (e: React.ChangeEvent<HTMLFormElement>) => {
      e.preventDefault();
      const _errors = preSubmitCheck();
      if (Object.values(_errors).some((_error) => _error !== '')) {
        return;
      }
      setProgressStatus(ProgressStatus.LOADING);
      contactAgentPM({
        email: formState.email,
        first_name: formState.firstName,
        last_name: formState.lastName,
        comment: formState.message,
        phone: formState.phone,
        contact_method: contactMethod,
        user: agent.user_id,
      })
        .then((data) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
        })
        .catch((e) => {
          console.log(e);
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    [
      preSubmitCheck,
      formState.email,
      formState.firstName,
      formState.lastName,
      formState.message,
      formState.phone,
      contactMethod,
      agent.user_id,
    ],
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );
  return (
    <Modal open={open} onClose={onClose} className='md:rounded-md min-h-[100vh] md:min-h-full'>
      <div className='px-4 md:px-5 py-5'>
        <div className='flex items-center justify-between mb-5'>
          <p className='text-2xl font-medium leading-[34px]'>Contact {agent.first_name}</p>
          <XMarkIcon onClick={onClose} className='w-5 h-5 cursor-pointer text-gray-main' />
        </div>
        {progressStatus === ProgressStatus.SUCCESSFUL ? (
          <div className='text-green-500 flex items-center'>
            <CheckCircleIcon className='w-5 h-5' />
            <span className='ml-2'>Request Successfully Sent</span>
          </div>
        ) : (
          <form onSubmit={onSubmit}>
            <div className='pb-6'>
              <div className='mb-6'>
                <Input
                  name='firstName'
                  type='text'
                  label='First Name'
                  className='p-2 w-full text-sm'
                  helperText={errors?.firstName ?? ''}
                  error={!!errors?.firstName?.length}
                  onChange={onChangeTextInput}
                />
              </div>
              <div className='mb-6'>
                <Input
                  name='lastName'
                  type='text'
                  label='Last Name'
                  className='p-2 w-full text-sm'
                  helperText={errors?.lastName ?? ''}
                  error={!!errors?.lastName?.length}
                  onChange={onChangeTextInput}
                />
              </div>
              <div className='mb-6'>
                <Input
                  name='email'
                  type='email'
                  label='Email'
                  className='p-2 w-full text-sm'
                  helperText={errors?.email ?? ''}
                  error={!!errors?.email?.length}
                  onChange={onChangeTextInput}
                />
              </div>
              <div className='mb-6'>
                <Input
                  name='phone'
                  type='text'
                  label='Phone Number'
                  className='p-2 w-full text-sm'
                  helperText={errors?.phone ?? ''}
                  error={!!errors?.phone?.length}
                  onChange={onChangeTextInput}
                />
              </div>
              <div className='mb-6'>
                <InputLabel error={!!errors?.message?.length}>How can I help?</InputLabel>
                <Textarea
                  name='message'
                  className='w-full p-2'
                  type='textarea'
                  placeholder='How can I help?'
                  helperText={errors?.message ?? ''}
                  error={!!errors?.message?.length}
                  onChange={onChangeTextInput}
                />
              </div>
              <div className='mb-6'>
                <InputLabel>How would you like to be contacted?</InputLabel>
                <div className='flex items-center'>
                  <div
                    className={classNames(
                      'text-sm leading-[30px] px-3 bg-light-slate mr-2.5 rounded cursor-pointer',
                      contactMethod === ContactMethod.PHONE &&
                        'text-primary-slate border-primary-slate border',
                    )}
                    onClick={() => setContactMethod(ContactMethod.PHONE)}
                  >
                    Phone
                  </div>
                  <div
                    className={classNames(
                      'text-sm leading-[30px] px-3 bg-light-slate mr-2.5 rounded cursor-pointer',
                      contactMethod === ContactMethod.TEXT &&
                        'text-primary-slate border-primary-slate border',
                    )}
                    onClick={() => setContactMethod(ContactMethod.TEXT)}
                  >
                    Text
                  </div>
                  <div
                    className={classNames(
                      'text-sm leading-[30px] px-3 bg-light-slate mr-2.5 rounded cursor-pointer',
                      contactMethod === ContactMethod.EMAIL &&
                        'text-primary-slate border-primary-slate border',
                    )}
                    onClick={() => setContactMethod(ContactMethod.EMAIL)}
                  >
                    Email
                  </div>
                </div>
              </div>
              {progressStatus === ProgressStatus.FAILED && (
                <InputLabel error>Failed to send request</InputLabel>
              )}
            </div>
            <Button
              disabled={progressStatus === ProgressStatus.LOADING}
              title={progressStatus === ProgressStatus.LOADING ? 'Submitting...' : 'Submit'}
              className='h-[50px] w-full'
              isSubmit
            />
          </form>
        )}
      </div>
    </Modal>
  );
};

export default ContactAgentDialog;
