'use client';

import { useState } from 'react';

import { Icon } from '@/clients/components/icon';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import RentalsFilterDialog from '../rentals-top-filters/RentalsFilterDialog';

enum FilterType {
  RENTALS = 'RENTALS',
  REAL_ESTATE = 'REAL_ESTATE',
}

type Props = {
  title: string;
  propertyCount: number;
  filterType: FilterType;
};

const RentalFilters = ({ title, filterType }: Props) => {
  const [showFilters, setShowFilters] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const onClose = () => {
    setShowFilters(false);
  };

  const onShowFilters = () => {
    setShowFilters(true);
  };

  const onSort = ({ target: { value } }: React.ChangeEvent<HTMLSelectElement>) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    if (!value) {
      current.delete('ordering');
    } else {
      current.set('ordering', value);
    }

    const search = current.toString();
    const query = search ? `?${search}` : '';
    const route = `${pathname}${query}`;

    router.push(route);
  };

  const value = searchParams.get('ordering') ?? '';

  return (
    <div className='mb-5 items-center justify-between md:flex lg:mb-7 lg:justify-end'>
      <div className='flex w-full flex-col items-end gap-y-5 lg:flex-row lg:items-center lg:justify-between'>
        <h3 className='text-2xl font-medium'>{title}</h3>
        <div className='flex max-w-fit gap-x-2.5'>
          <div
            onClick={onShowFilters}
            className='flex h-12 cursor-pointer items-center justify-between rounded border border-zinc-200 px-2.5 py-[5px]'
          >
            <Icon icon='filter' />
            <div className='px-2'>Filters</div>
            <Icon icon='arrow-down' />
          </div>
          <select
            style={{
              background: 'url("/images/icons/arrow-down.svg") no-repeat 95% 50%',
              backgroundSize: '18px',
            }}
            className='h-12 px-2 rounded border border-zinc-200 justify-end ordering appearance-none'
            name='ordering'
            onChange={onSort}
            value={value}
          >
            <option value=''>Sort By</option>
            <option value='-peak_rate'>Peak Price High to Low</option>
            <option value='peak_rate'>Peak Price Low to High</option>
            <option value='-bedroom_number'>Bedrooms High to High</option>
            <option value='bedroom_number'>Bedrooms Low to High</option>
            <option value='area__name'>Area</option>
          </select>
        </div>
      </div>
      {showFilters && (
        <>{filterType === FilterType.RENTALS && <RentalsFilterDialog onClose={onClose} />}</>
      )}
    </div>
  );
};

export default RentalFilters;
