'use client';

import { ReactNode, useEffect } from 'react';

import { RentalPropertyProps } from '@/app/components/rental-listing/PropertyCard';
import { SegmentEvents } from '@/types/analytics';

type Props = {
  children: ReactNode;
  rentalListings: RentalPropertyProps[];
  searchParams: { offset: number };
};

const ClientWrapper = ({ children, rentalListings, searchParams }: Props) => {
  useEffect(() => {
    window?.analytics?.track(SegmentEvents.PRODUCT_LIST_VIEWED, {
      action: 'Viewed',
      category: 'Product List',
      object: 'Product List',
      list_id: rentalListings.map((_listing) => _listing.listing_id),
      products: rentalListings.map((_listing, index) => ({
        product_id: _listing.listing_id,
        name: _listing.address,
        category: _listing.area_name,
        position: index + 1,
        price: _listing.max_price,
        quantity: '1',
        brand: _listing.owner_email ?? '',
        image_url: _listing.url,
      })),
    });
  }, [rentalListings]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [searchParams]);

  return children;
};

export default ClientWrapper;
