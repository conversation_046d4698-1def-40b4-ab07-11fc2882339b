'use client';

import React, { useCallback, useState } from 'react';

import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

type ContactAgentPayload = {
  comment: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  agent_name?: string;
  contact_method?: string;
  neighborhood?: string;
  property_address?: string;
};
export const contactAgent = async (data: ContactAgentPayload) => {
  const res = await fetch(`${BASE_URL}/contact-agent`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Contact Agent Request');
  }

  return res.json();
};

const ContactAgentForm = () => {
  const [progressStatus, setProgressStatus] = React.useState<ProgressStatus | null>(null);
  const { formState, errors, onResetForm, onChange, preSubmitCheck } = useForm<ContactAgentPayload>(
    {
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      comment: '',
    },
    {
      first_name: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Name is required.`;
        }
      },
      last_name: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Last name is required.`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Email is required.`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Phone number is required.`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      comment: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Message is required.`;
        }
      },
    },
  );

  const onSubmit = useCallback(
    (e: React.ChangeEvent<HTMLFormElement>) => {
      e.preventDefault();
      const _errors = preSubmitCheck();
      if (Object.values(_errors).some((_error) => _error !== '')) {
        return;
      }
      setProgressStatus(ProgressStatus.LOADING);
      contactAgent({
        email: formState.email,
        first_name: formState.first_name,
        last_name: formState.last_name,
        property_address: formState.property_address ?? '',
        phone: formState.phone,
        comment: formState.comment,
        contact_method: 'phone',
      })
        .then((data) => {
          if (data.state === 'success') {
            setProgressStatus(ProgressStatus.SUCCESSFUL);
            window?.analytics?.track(SegmentEvents.SELL_FORM_SUBMITTED, {
              email: formState.email,
              first_name: formState.first_name,
              last_name: formState.last_name,
              how_can_we_help: formState.comment,
              phone: formState.phone,
              property_address: formState.property_address ?? '',
              url: document.URL,
              referrer: document.referrer,
            });
            onResetForm();
          }
        })
        .catch(() => {
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    [preSubmitCheck, formState, onResetForm],
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );

  return (
    <>
      <form onSubmit={onSubmit} className='lg:w-[730px] lg:grid lg:grid-cols-2 lg:gap-x-7'>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-2 text-gray-80'
            type='text'
            placeholder='Name'
            name='first_name'
            helperText={errors?.first_name ?? ''}
            error={!!errors?.first_name?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-2 text-gray-80'
            type='text'
            placeholder='Last name'
            name='last_name'
            helperText={errors?.last_name ?? ''}
            error={!!errors?.last_name?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-2 text-gray-80'
            type='text'
            placeholder='Email'
            name='email'
            helperText={errors?.email ?? ''}
            error={!!errors?.email?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-2 text-gray-80'
            type='text'
            placeholder='Phone'
            name='phone'
            helperText={errors?.phone ?? ''}
            error={!!errors?.phone?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div className='col-span-2'>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-2 text-gray-80'
            type='text'
            placeholder='Property address (optional)'
            name='property_address'
            helperText={errors?.property_address ?? ''}
            error={!!errors?.property_address?.length}
            onChange={onChangeTextInput}
          />
        </div>

        <div className='md:col-span-2'>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-2 text-gray-80'
            type='text'
            placeholder={'I am interested in selling my Nantucket Property.'}
            name='comment'
            helperText={errors?.comment ?? ''}
            error={!!errors?.comment?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <Button title='Contact Agent' className='w-full py-2 lg:col-span-2' isSubmit />
      </form>
      <div>
        <p className='text-sm mt-4'>
          <span>{progressStatus === ProgressStatus.LOADING ? 'Loading...' : ''}</span>
          <span>
            {progressStatus === ProgressStatus.SUCCESSFUL ? 'Form submitted successfully.' : ''}
          </span>
          <span>
            {progressStatus === ProgressStatus.FAILED
              ? 'Something went wrong. Please try again later.'
              : ''}
          </span>
        </p>
      </div>
    </>
  );
};

export default ContactAgentForm;
