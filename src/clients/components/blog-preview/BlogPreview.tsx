type BlogPreviewProps = {
  children: React.ReactNode;
};

export const BlogPreview = ({ children }: BlogPreviewProps) => {
  return (
    <div className='py-12 px-4 md:pl-8'>
      <div className='xl:container'>
        <div className='mb-8 pr-4 md:mb-16'>
          <h3 className='text-2xl xl:text-4xl text-center font-semibold'>
            News and Notes from the Congdon & Coleman Blog
          </h3>
        </div>

        <div className='pb-6 px-1 hidden xl:block'>{children}</div>

        <div className='flex flex-nowrap overflow-auto pb-6 gap-x-5 px-1 md:justify-between xl:hidden no-scrollbar'>
          {children}
        </div>
      </div>
    </div>
  );
};
