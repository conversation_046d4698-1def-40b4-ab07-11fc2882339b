'use client';

import { ReactNode, useState } from 'react';

import { VIRTUAL_TOUR_ID } from '@/app/components/rental-listing-details/MediaView';
import { IListingImage } from '@/types/rental-listing-details';

import dynamic from 'next/dynamic';

type Props = {
  children: ReactNode;
  images: IListingImage[];
  propertyHeadline?: string;
  virtual_tour_link?: string;
};

const MobileViewAllPhotos = dynamic(() => import('./MobileViewAllPhotos'), {
  ssr: false,
});

const MobileVirtualTourModal = dynamic(() => import('./MobileVirtualTourModal'), {
  ssr: false,
});

const MobileMediaView = ({ children, images, propertyHeadline, virtual_tour_link }: Props) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showVirtualTour, setShowVirtualTour] = useState<boolean>(false);

  return (
    <>
      <div
        className='cursor relative h-[280px] w-full'
        onClick={(e: any) => {
          console.log('the e is', e.target.id);
          if (e.target.id === VIRTUAL_TOUR_ID) {
            setShowVirtualTour(true);
            return;
          }
          e.stopPropagation();
          setShowModal(true);
        }}
      >
        {children}
      </div>
      {showModal && (
        <MobileViewAllPhotos
          open={showModal}
          onClose={() => setShowModal(false)}
          images={images}
          propertyHeadline={propertyHeadline}
        />
      )}
      {showVirtualTour && (
        <MobileVirtualTourModal
          open={showVirtualTour}
          onClose={() => setShowVirtualTour(false)}
          virtual_tour_link={virtual_tour_link ?? ''}
          propertyHeadline={propertyHeadline}
        />
      )}
    </>
  );
};

export default MobileMediaView;
