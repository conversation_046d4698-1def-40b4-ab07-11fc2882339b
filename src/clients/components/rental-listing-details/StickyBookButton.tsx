'use client';

import React, { useContext } from 'react';

import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import Button from '@/clients/ui/button';

import Image from 'next/image';

const StickyBookButton = () => {
  const { checkoutRef, setOpenContactAgentForm } = useContext(RentalDetailsContext);
  return (
    <div className='fixed bottom-0 left-0 right-0 z-[5] flex items-center gap-4 bg-white px-4 py-5 shadow md:hidden'>
      <Button
        onClick={() =>
          checkoutRef.current &&
          checkoutRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
        title='Request Booking'
        className='w-10/12'
      />
      <Button
        intent='outline'
        icon={
          <Image
            alt='Message Question icon'
            src='/images/icons/message-question.svg'
            className='mr-0'
            width={24}
            height={24}
          />
        }
        onClick={() => setOpenContactAgentForm(true)}
        className='bg-white'
      />
    </div>
  );
};

export default StickyBookButton;
