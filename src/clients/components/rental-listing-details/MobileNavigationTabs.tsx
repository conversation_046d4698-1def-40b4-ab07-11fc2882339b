'use client';

import React, { useContext } from 'react';

import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';

const MobileNavigationTabs = () => {
  const { locationRef, propertyInfoRef, availabilityRef } = useContext(RentalDetailsContext);
  return (
    <div className='fixed top-[80px] overflow-hidden left-0 right-0 z-[5] flex md:hidden bg-olive text-base font-medium text-white items-center justify-between w-full]'>
      <div
        className='flex-1 text-center py-2'
        onClick={() =>
          propertyInfoRef.current &&
          propertyInfoRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      >
        Property Info
      </div>
      <div
        className='flex-1 text-center py-2'
        onClick={() =>
          locationRef.current &&
          locationRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      >
        Location
      </div>
      <div
        className='flex-1 text-center py-2'
        onClick={() =>
          availabilityRef.current &&
          availabilityRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      >
        Availability
      </div>
    </div>
  );
};

export default MobileNavigationTabs;
