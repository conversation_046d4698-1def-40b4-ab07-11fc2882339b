'use client';

import { Tab } from '@headlessui/react';

import { IListingDetails } from '@/types/rental-listing-details';

import classNames from 'classnames';

import DescriptionText from './DescriptionText';

type Props = {
  details: IListingDetails;
};
const PropertyDescription = ({ details }: Props) => {
  return (
    <div className='my-5 border-b pb-5'>
      <Tab.Group>
        <Tab.List className='flex max-w-full border-b pb-2 md:max-w-max'>
          <Tab
            key='description'
            id='description-TAB'
            className={({ selected }) =>
              classNames(
                'flex-1 text-left text-sm font-medium leading-[120%] focus-visible:outline-none md:mr-6 md:flex-auto md:text-center',
                selected &&
                  (details?.first_floor ||
                    details?.second_floor ||
                    details?.third_floor ||
                    details?.lower_level)
                  ? 'text-[#4C737F]'
                  : 'cursor-default text-[#09182C]',
              )
            }
          >
            Description
          </Tab>
          {details?.first_floor && (
            <Tab
              key='first-floor'
              id='first-floor-TAB'
              className={({ selected }) =>
                classNames(
                  'flex-1 text-sm font-medium leading-[120%] focus-visible:outline-none md:mr-6 md:flex-auto',
                  selected ? 'text-[#4C737F]' : 'text-[#09182C]',
                )
              }
            >
              First floor
            </Tab>
          )}
          {details?.second_floor && (
            <Tab
              key='second-floor'
              id='second-floor-TAB'
              className={({ selected }) =>
                classNames(
                  'flex-1 text-sm font-medium leading-[120%] focus-visible:outline-none md:mr-6 md:flex-auto',
                  selected ? 'text-[#4C737F]' : 'text-[#09182C]',
                )
              }
            >
              Second floor
            </Tab>
          )}
          {details?.third_floor && (
            <Tab
              key='third-floor'
              id='third-floor-TAB'
              className={({ selected }) =>
                classNames(
                  'flex-1 text-sm font-medium leading-[120%] focus-visible:outline-none md:mr-6 md:flex-auto',
                  selected ? 'text-[#4C737F]' : 'text-[#09182C]',
                )
              }
            >
              Third floor
            </Tab>
          )}

          {details?.lower_level && (
            <Tab
              key='basement'
              id='basement-TAB'
              className={({ selected }) =>
                classNames(
                  'flex-1 text-sm font-medium leading-[120%] focus-visible:outline-none md:mr-6 md:flex-auto',
                  selected ? 'text-[#4C737F]' : 'text-[#09182C]',
                )
              }
            >
              Basement
            </Tab>
          )}
        </Tab.List>
        <Tab.Panels className='mt-5'>
          <Tab.Panel key='description' id='description-TAB_PANEL'>
            <DescriptionText description={details?.description ?? ''} />
          </Tab.Panel>
          <Tab.Panel key='first-floor' id='first-floor-TAB_PANEL'>
            <DescriptionText description={details?.first_floor ?? ''} />
          </Tab.Panel>
          <Tab.Panel key='second-floor' id='second-floor-TAB_PANEL'>
            <DescriptionText description={details?.second_floor ?? ''} />
          </Tab.Panel>
          <Tab.Panel key='third-floor' id='third-floor-TAB_PANEL'>
            <DescriptionText description={details?.third_floor ?? ''} />
          </Tab.Panel>
          <Tab.Panel key='basement' id='basement-TAB_PANEL'>
            <DescriptionText description={details?.lower_level ?? ''} />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
};

export default PropertyDescription;
