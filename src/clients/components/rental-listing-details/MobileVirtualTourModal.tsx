import { XMarkIcon } from '@heroicons/react/20/solid';

import Modal from '@/clients/ui/modal';

type Props = {
  open: boolean;
  onClose: () => void;
  virtual_tour_link: string;
  propertyHeadline?: string;
};

const MobileVirtualTourModal = ({ open, onClose, virtual_tour_link, propertyHeadline }: Props) => {
  return (
    <Modal open={open} onClose={onClose}>
      <div className='flex h-[100vh] flex-col'>
        <div className='flex min-h-[80px] w-full items-center justify-between shadow-header'>
          <p className='ml-4 text-lg font-medium'>{propertyHeadline}</p>
          <div className='z-[1] mr-4 h-6 w-6 cursor-pointer text-gray-main' onClick={onClose}>
            <XMarkIcon className='fill-current' />
          </div>
        </div>

        <iframe className='w-full h-full' title='Virtual tour' src={virtual_tour_link} />
      </div>
    </Modal>
  );
};

export default MobileVirtualTourModal;
