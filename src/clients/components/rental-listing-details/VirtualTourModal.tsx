'use client';

import Modal from '@/clients/ui/modal';

type Props = {
  open: boolean;
  onClose: () => void;
  virtual_tour_link: string;
};

const VirtualTourModal = ({ open, onClose, virtual_tour_link }: Props) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      className='min-w-[80%] bg-transparent px-12 shadow-none'
      showClose
    >
      <iframe className='w-[80vw] h-[80vh]' title='Virtual tour' src={virtual_tour_link} />
    </Modal>
  );
};

export default VirtualTourModal;
