'use client';

import React, { useCallback, useContext, useMemo, useState } from 'react';

import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/20/solid';

import Calendar from '@/clients/components/calendar';
import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  getPropertyDetailsCalendarMonths,
  getSelectedAvailabilityMonthnumber,
} from '@/utils/calendar';

type Props = {
  details: IListingDetails;
  isMobile?: boolean;
};

const PropertyRatesAndAvailability = ({ isMobile, details }: Props) => {
  const { availabilityRef } = useContext(RentalDetailsContext);
  const monthList = useMemo(() => getPropertyDetailsCalendarMonths(), []);
  const [current, setCurrent] = useState(getSelectedAvailabilityMonthnumber(monthList));
  const currentMonth = monthList[current];
  const isEvenMonths = useMemo(() => monthList.length % 2 === 0, [monthList.length]);
  const isLastSlide = useMemo(
    () => current === (!isMobile && isEvenMonths ? monthList.length - 2 : monthList.length - 1),
    [current, isEvenMonths, isMobile, monthList.length],
  );

  const handleNext = useCallback(() => {
    if (!isMobile) {
      const isOddIterationLast = current % 2 !== 0 && current === monthList.length - 2;
      if (isOddIterationLast || (isLastSlide && isEvenMonths)) {
        setCurrent(0);
        return;
      }

      if (isLastSlide && !isEvenMonths) {
        setCurrent(1);
        return;
      }

      setCurrent(current + 2);
    } else {
      setCurrent(isLastSlide ? 0 : current + 1);
    }
  }, [isMobile, current, monthList.length, isLastSlide, isEvenMonths]);

  const handlePrev = useCallback(() => {
    const isFirstSlide = current === 0;
    if (!isMobile) {
      if (current === 1) {
        setCurrent(monthList.length - 1);
        return;
      }

      if (current === 0) {
        setCurrent(monthList.length - 2);
        return;
      }

      setCurrent(current - 2);
    } else {
      setCurrent(isFirstSlide ? monthList.length - 1 : current - 1);
    }
  }, [current, isMobile, monthList.length]);

  return (
    <div className='my-5 mb-0' ref={availabilityRef}>
      <p className='mb-5 text-lg font-medium leading-[175%]'>Rates and Availability</p>
      <div className='relative transition-all'>
        <ChevronLeftIcon
          className='absolute left-1 top-0 h-6 w-auto cursor-pointer text-olive md:left-3'
          onClick={handlePrev}
        />
        <ChevronRightIcon
          className='absolute right-1 top-0 h-6 w-auto cursor-pointer text-olive md:right-3'
          onClick={handleNext}
        />
        {isMobile ? (
          <Calendar
            month={currentMonth.month}
            year={currentMonth.year}
            availableRanges={details.availabilities}
            rateRanges={details.rates}
          />
        ) : (
          <div className='flex gap-4'>
            <div className='flex-1'>
              <Calendar
                month={currentMonth.month}
                year={currentMonth.year}
                availableRanges={details.availabilities}
                rateRanges={details.rates}
              />
            </div>
            <div className='flex-1'>
              <Calendar
                month={
                  isLastSlide && !isEvenMonths ? monthList[0]?.month : monthList[current + 1]?.month
                }
                year={
                  isLastSlide && !isEvenMonths ? monthList[0]?.year : monthList[current + 1]?.year
                }
                availableRanges={details.availabilities}
                rateRanges={details.rates}
              />
            </div>
          </div>
        )}
      </div>
      <p className='mt-5 text-xs text-gray-main'>
        Information is believed to be accurate but is not guaranteed. Rates and availability must be
        confirmed prior to booking.
      </p>
    </div>
  );
};

export default PropertyRatesAndAvailability;
