import React, { useCallback, useMemo, useState } from 'react';

const DescriptionText = ({ description = '' }: { description: string }) => {
  const [less, setLess] = useState(description?.length > 400);

  const text = useMemo(() => {
    if (less) {
      return description?.slice(0, 400);
    }
    return description;
  }, [less, description]);

  const handleMore = useCallback(() => {
    setLess(false);
  }, []);

  return (
    <div className='text-sm leading-[130%] text-[#6D7380]'>
      {text}
      {less && (
        <span onClick={handleMore} className='cursor-pointer pl-1 text-blue-main underline'>
          + Read more
        </span>
      )}
    </div>
  );
};

export default DescriptionText;
