import { <PERSON>Validator } from '@/clients/hooks/useForm';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';

import dayjs from 'dayjs';

import { DateRange } from '../../date-range-picker';

import { FormValues } from '.';

export const getFormValidators = (capacity: number): FormValidator<FormValues> => {
  return {
    dateRange: (_v, _n, _value) => {
      if ((_value as DateRange)?.every((_el) => _el == null)) {
        return 'Required';
      }

      if (!(_value as DateRange)?.some((_v) => dayjs(_v).isValid())) {
        return 'Invalid Range.';
      }
    },
    adults: (_v, _n, _value) => {
      if (!_value) {
        return 'Required';
      }

      if ((_value as number) < 1) {
        return 'At least one adult is required for booking';
      }
    },
    children: (_v, _n, _value) => {
      if (Number(_value) + Number(_v.adults) > capacity) {
        return `Maximum guests for this property is ${capacity}`;
      }
    },
    firstName: (_v, _n, _value) => {
      if (_value.trim().length === 0) {
        return `Firstname is required.`;
      }
    },
    lastName: (_v, _n, _value) => {
      if (_value.trim().length === 0) {
        return `Lastname is required.`;
      }
    },
    email: (_v, _n, _value: string) => {
      if (_value.trim().length === 0) {
        return `Email is required.`;
      }

      if (!_value.match(EMAIL_PATTERN)) {
        return 'Invalid email address';
      }
    },
    phone: (_v, _n, _value: string) => {
      if (_value.trim().length === 0) {
        return `Phone number is required.`;
      }

      if (!_value.match(PHONE_NUMBER_PATTERN)) {
        return 'Invalid phone number';
      }
    },
    message: (_v, _n, _value: string) => {
      if (_value.trim().length === 0) {
        return `Message is required.`;
      }
    },
  };
};
