'use client';

import React, { useCallback, useMemo, useState } from 'react';

import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

import { submitBookingRequest } from '@/app/services/rental-listing-details';
import InputLabel from '@/app/ui/input-label';
import DateRangePicker, { DateRange } from '@/clients/components/date-range-picker';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import Modal from '@/clients/ui/modal';
import Select, { SelectOption } from '@/clients/ui/select';
import Textarea from '@/clients/ui/textarea';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  formatAvailabilityDateString,
  getDatesBetweenDates,
  rangesOverlap,
} from '@/utils/availability';
import { checkIfDateHasRate, getRentalStartMonth, isBetweenTwoDates } from '@/utils/calendar';

import dayjs from 'dayjs';

import { getFormValidators } from './helpers';

type Props = {
  open: boolean;
  onClose?: () => void;
  details: IListingDetails;
  isMobile?: boolean;
  dateRange?: DateRange;
  adultsCount?: number;
  childrenCount?: number;
};

export type FormValues = {
  dateRange: DateRange;
  adults: number;
  children: number;
  firstName: string;
  lastName: string;
  email: string;
  message: string;
  phone: string;
};

const RequestBookingForm = ({
  open,
  onClose,
  details,
  isMobile,
  dateRange = [null, null],
  adultsCount = 1,
  childrenCount = 0,
}: Props) => {
  const {
    capacity,
    listing_id,
    address,
    area: { name: neighborhood },
    availabilities,
    rates,
  } = details;
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const rentalRates = useMemo(() => rates ?? [], [rates]);
  const { formState, pristine, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      dateRange,
      adults: adultsCount,
      children: childrenCount,
      firstName: '',
      lastName: '',
      email: '',
      message: '',
      phone: '',
    },
    getFormValidators(capacity),
  );

  const isDateEnabled = useCallback(
    (date: Date) => {
      const hasRate = checkIfDateHasRate(dayjs(date), rentalRates);
      const isBooked = availabilities.some((_avail) =>
        isBetweenTwoDates(
          dayjs(_avail.from_date).add(1, 'day').toISOString(),
          dayjs(_avail.to_date).subtract(1, 'day').toISOString(),
          dayjs(date),
        ),
      );
      return !isBooked && hasRate;
    },
    [availabilities, rentalRates],
  );

  const checkPreventOnChange = useCallback(
    (range: DateRange) => {
      if (range?.some((_v) => !dayjs(_v).isValid())) {
        return false;
      }

      const someRangesOverlap = !!availabilities.some((_range) =>
        rangesOverlap(
          [
            formatAvailabilityDateString(_range.from_date),
            formatAvailabilityDateString(_range.to_date),
          ],
          range,
          true,
        ),
      );

      const datesBetween = getDatesBetweenDates(dayjs(range?.[0]), dayjs(range?.[1]));
      const someDatesAreEmptyRate = datesBetween.some(
        (_date) => !checkIfDateHasRate(_date, rentalRates),
      );

      return someRangesOverlap || someDatesAreEmptyRate;
    },
    [availabilities, rentalRates],
  );

  const onSubmit = useCallback(
    (e: React.ChangeEvent<HTMLFormElement>) => {
      e.preventDefault();
      const _errors = preSubmitCheck();
      if (Object.values(_errors).some((_error) => _error !== '')) {
        return;
      }
      setProgressStatus(ProgressStatus.LOADING);
      const hasValidDates =
        formState.dateRange && formState.dateRange?.every((_v) => dayjs(_v).isValid());

      submitBookingRequest({
        email: formState.email ?? '',
        phone: formState.phone ?? '',
        first_name: formState.firstName ?? '',
        last_name: formState.lastName ?? '',
        comment: formState.message ?? '',
        neighborhood,
        listing_id,
        guest: formState.adults ?? 1,
        children: formState?.children ?? 0,
        property_address: address,
        interest: 'rentals',
        arrival_date: hasValidDates
          ? dayjs(formState.dateRange[0]).format('YYYY-MM-DD')
          : undefined,
        departure_date: hasValidDates
          ? dayjs(formState.dateRange[1]).format('YYYY-MM-DD')
          : undefined,
        contact_method: 'email',
      })
        .then(() => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          window?.analytics?.track(SegmentEvents.BOOKING_REQUESTED, {
            amenities: details.featured_amenities,
            capacity: details.capacity,
            checkin_date: hasValidDates ? dayjs(formState.dateRange[0]).format('YYYY-MM-DD') : null,
            checkout_date: hasValidDates
              ? dayjs(formState.dateRange[1]).format('YYYY-MM-DD')
              : null,
            listing_id: details.listing_id,
            listing_name: details.address,
            listing_number_of_bedrooms: details.bedroom_number,
            neighborhood: details.area_name,
            num_adults: formState?.adults ?? 1,
            num_children: formState?.children ?? 0,
            number_of_days: dayjs(formState.dateRange[1]).diff(formState.dateRange[0], 'days'),
            number_of_guests: Number(formState?.adults ?? 1) + Number(formState?.children ?? 0),
            price: details.peak_rate,
            region: 'Massachusetts',
            city: 'Nantucket',
            country: 'United States',
            url: document.URL,
            referrer: document.referrer,
          });
        })
        .catch((e) => {
          console.log(e);
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    [preSubmitCheck, formState, neighborhood, listing_id, address, details],
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );

  const adultsOptions = useMemo(() => {
    const options: SelectOption[] = [];
    for (let i = 1; i <= capacity; i++) {
      options.push({ text: `${i}`, value: `${i}` });
    }
    return options;
  }, [capacity]);

  const childrenOptions = useMemo(() => {
    const options: SelectOption[] = [];
    for (let i = 0; i <= capacity; i++) {
      options.push({ text: `${i}`, value: `${i}` });
    }
    return options;
  }, [capacity]);

  return (
    <Modal open={open} onClose={onClose} className='md:rounded-md min-h-[100vh] md:min-h-full'>
      <div className='px-4 md:px-5 py-5'>
        <div className='flex items-center justify-between mb-5'>
          <p className='text-2xl font-medium leading-[34px]'>Request Booking</p>
          <XMarkIcon onClick={onClose} className='w-5 h-5 cursor-pointer text-gray-main' />
        </div>
        {progressStatus === ProgressStatus.SUCCESSFUL ? (
          <div className='text-green-500 flex items-center'>
            <CheckCircleIcon className='w-5 h-5' />
            <span className='ml-2'>Request Successfully Sent</span>
          </div>
        ) : (
          <form onSubmit={onSubmit}>
            <div className='mb-5'>
              <p className='font-semibold leading-[175%] mb-5 text-dark-blue'>Trip Details</p>
              <div className='relative flex items-center justify-between text-gray-80 mb-7'>
                <p>Travel Dates</p>
                <DateRangePicker
                  name='dateRange'
                  isMobile={isMobile}
                  onChange={onChange}
                  filterDate={isDateEnabled}
                  preventOnChange={checkPreventOnChange}
                  value={formState.dateRange}
                  className='p-2 mb-0 static md:relative'
                  helperText={errors?.dateRange ?? ''}
                  error={!!errors?.dateRange?.length}
                  maxDate={dayjs()
                    .startOf('year')
                    .add(1, 'years')
                    .add(11, 'months')
                    .endOf('month')
                    .toDate()}
                />
              </div>
              <div className='flex items-center justify-between text-gray-80 mb-7'>
                <p>Adults (18+)</p>
                <Select
                  className='p-2 text-center'
                  label='Adults'
                  options={adultsOptions}
                  name='adults'
                  value={formState.adults}
                  onChange={onChange}
                  helperText={errors?.adults ?? ''}
                  error={!!errors?.adults?.length}
                />
              </div>
              <div className='flex items-center justify-between text-gray-80 mb-7'>
                <p>Children (0-17)</p>
                <Select
                  className='p-2 text-center'
                  label='Children'
                  options={childrenOptions}
                  name='children'
                  value={formState.children}
                  onChange={onChange}
                  helperText={errors?.children ?? ''}
                  error={!!errors?.children?.length}
                />
              </div>
              <div className='flex items-center justify-between text-gray-80'>
                <p>Total Guests</p>
                <p className='min-w-[120px] text-center'>
                  {Number(formState.adults) + Number(formState.children)}
                </p>
              </div>
            </div>
            <div className='mb-5'>
              <p className='font-semibold leading-[175%] mb-5 text-dark-blue mb-4'>
                Traveler Information{' '}
              </p>
              <div className='grid grid-cols-2 gap-2 w-full mb-7'>
                <Input
                  name='firstName'
                  type='text'
                  label='First Name'
                  className='p-2 w-full text-sm'
                  helperText={errors?.firstName ?? ''}
                  error={!!errors?.firstName?.length}
                  onChange={onChangeTextInput}
                  required
                />
                <Input
                  name='lastName'
                  type='text'
                  label='Last Name'
                  className='p-2 w-full text-sm'
                  helperText={errors?.lastName ?? ''}
                  error={!!errors?.lastName?.length}
                  onChange={onChangeTextInput}
                  required
                />
              </div>
              <div className='grid grid-cols-2 gap-2 w-full mb-7'>
                <Input
                  name='email'
                  type='email'
                  label='Email'
                  className='p-2 w-full text-sm'
                  helperText={errors?.email ?? ''}
                  error={!!errors?.email?.length}
                  onChange={onChangeTextInput}
                  required
                />
                <Input
                  name='phone'
                  type='text'
                  label='Phone Number'
                  className='p-2 w-full text-sm'
                  helperText={errors?.phone ?? ''}
                  error={!!errors?.phone?.length}
                  onChange={onChangeTextInput}
                  required
                />
              </div>
            </div>
            <div className='mb-7'>
              <p className='font-semibold leading-[175%] text-dark-blue mb-2.5'>
                Send a message to the Agent<span className='text-red-main'>*</span>
              </p>
              <InputLabel className='text-gray-80'>
                Tell them about who are travelling with and why you chose this property.
              </InputLabel>
              <Textarea
                name='message'
                className='w-full p-2'
                type='textarea'
                helperText={errors?.message ?? ''}
                error={!!errors?.message?.length}
                onChange={onChangeTextInput}
              />
            </div>
            {progressStatus === ProgressStatus.FAILED && (
              <InputLabel error className='my-5'>
                Failed to send request
              </InputLabel>
            )}
            <Button
              disabled={progressStatus === ProgressStatus.LOADING}
              title={
                progressStatus === ProgressStatus.LOADING
                  ? 'Submitting...'
                  : 'Submit Booking Request'
              }
              className='h-[50px] w-full'
              isSubmit
            />
          </form>
        )}
      </div>
    </Modal>
  );
};

export default RequestBookingForm;
