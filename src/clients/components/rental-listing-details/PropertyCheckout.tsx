'use client';

import { useCallback, useContext, useMemo, useState } from 'react';

import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import Button from '@/clients/ui/button';
import { SegmentEvents } from '@/types/analytics';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  formatAvailabilityDateString,
  getDatesBetweenDates,
  rangesOverlap,
} from '@/utils/availability';
import { checkIfDateHasRate, getRentalStartMonth, isBetweenTwoDates } from '@/utils/calendar';

import dayjs from 'dayjs';
import dynamic from 'next/dynamic';

import DateRangePicker, { DateRange } from '../date-range-picker';
import GuestSelector, { GuestsValues } from '../guest-selector';

const RequestBookingForm = dynamic(() => import('./request-booking-from'), {
  ssr: false,
});
const ContactAgentForm = dynamic(() => import('./contact-agent-form'), {
  ssr: false,
});

type Props = {
  details: IListingDetails;
  isMobile?: boolean;
};

const PropertyCheckout = ({ details, isMobile }: Props) => {
  const { checkoutRef, openContactAgentForm, setOpenContactAgentForm } =
    useContext(RentalDetailsContext);
  const [openBookingForm, setOpenBookingForm] = useState<boolean>(false);
  const [guestCount, setGuestCount] = useState<GuestsValues>({
    adults: 1,
    children: 0,
  });
  const availabilities = useMemo(() => details?.availabilities ?? [], [details?.availabilities]);
  const rentalRates = useMemo(() => details?.rates ?? [], [details?.rates]);
  const [dateRange, setDateRange] = useState<DateRange>([getRentalStartMonth(), null]);

  const isDateEnabled = useCallback(
    (date: Date) => {
      const hasRate = checkIfDateHasRate(dayjs(date), rentalRates);
      const isBooked = availabilities.some((_avail) =>
        isBetweenTwoDates(
          dayjs(_avail.from_date).add(1, 'day').toISOString(),
          dayjs(_avail.to_date).subtract(1, 'day').toISOString(),
          dayjs(date),
        ),
      );
      return !isBooked && hasRate;
    },
    [availabilities, rentalRates],
  );

  const checkPreventOnChange = useCallback(
    (range: DateRange) => {
      if (range?.some((_v) => !dayjs(_v).isValid())) {
        return false;
      }

      const someRangesOverlap = !!availabilities.some((_range) =>
        rangesOverlap(
          [
            formatAvailabilityDateString(_range.from_date),
            formatAvailabilityDateString(_range.to_date),
          ],
          range,
          true,
        ),
      );

      const datesBetween = getDatesBetweenDates(dayjs(range?.[0]), dayjs(range?.[1]));
      const someDatesAreEmptyRate = datesBetween.some(
        (_date) => !checkIfDateHasRate(_date, rentalRates),
      );

      return someRangesOverlap || someDatesAreEmptyRate;
    },
    [availabilities, rentalRates],
  );

  const onChangeRange = useCallback((range: DateRange) => {
    setDateRange(range);
  }, []);

  const onClickRequestBooking = useCallback(() => {
    window?.analytics?.track(SegmentEvents.BOOKING_REQUEST_CLICKED, {
      action: 'CTA clicked',
      amenities: details.featured_amenities,
      capacity: details.capacity,
      checkin_date: dayjs(dateRange[0]).format('YYYY-MM-DD'),
      checkout_date: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      listing_id: details.listing_id,
      listing_name: details.address,
      listing_number_of_bedrooms: details.bedroom_number,
      neighborhood: details.area_name,
      num_adults: guestCount.adults ?? 1,
      num_children: guestCount.children ?? 0,
      number_of_days: dayjs(dateRange[1]).diff(dateRange[0], 'days'),
      number_of_guests: Number(guestCount.adults ?? 1) + Number(guestCount.children ?? 0),
      price: details.peak_rate,
      region: 'Massachusetts',
      city: 'Nantucket',
      country: 'United States',
      url: document.URL,
      referrer: document.referrer,
    });
    setOpenBookingForm(true);
  }, [dateRange, details, guestCount]);

  return (
    <>
      <Button
        title='Contact an Agent'
        className='w-full border-olive text-navy-blue hidden md:block'
        onClick={() => setOpenContactAgentForm(true)}
        intent='outline'
      />
      <div
        ref={checkoutRef}
        className='my-5 rounded border border-disabled bg-light-gray px-2.5 py-5'
      >
        <DateRangePicker
          value={dateRange}
          onChange={onChangeRange}
          preventOnChange={checkPreventOnChange}
          isMobile={isMobile}
          filterDate={isDateEnabled}
          maxDate={dayjs()
            .startOf('year')
            .add(1, 'years')
            .add(11, 'months')
            .endOf('month')
            .toDate()}
        />
        <GuestSelector
          guestCount={guestCount}
          onChange={setGuestCount}
          capacity={details.capacity}
        />
        <Button title='Request Booking' className='w-full' onClick={onClickRequestBooking} />
      </div>
      {openBookingForm && (
        <RequestBookingForm
          details={details}
          open={openBookingForm}
          isMobile={isMobile}
          onClose={() => setOpenBookingForm(false)}
          dateRange={dateRange}
          adultsCount={guestCount?.adults}
          childrenCount={guestCount?.children}
        />
      )}
      {openContactAgentForm && (
        <ContactAgentForm
          open={openContactAgentForm}
          onClose={() => setOpenContactAgentForm(false)}
          details={details}
        />
      )}
    </>
  );
};

export default PropertyCheckout;
