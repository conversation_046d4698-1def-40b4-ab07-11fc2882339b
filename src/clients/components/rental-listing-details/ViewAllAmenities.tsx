'use client';

import React, { ReactNode, useState } from 'react';

type Props = {
  allAmenitiesNode: ReactNode;
};

const ViewAllAmenities = ({ allAmenitiesNode }: Props) => {
  const [showAll, setShowAll] = useState<boolean>(false);
  return (
    <div className='mb-5'>
      {showAll ? (
        <>{allAmenitiesNode}</>
      ) : (
        <p className='cursor-pointer text-sm text-olive underline' onClick={() => setShowAll(true)}>
          + View More Amenities
        </p>
      )}
    </div>
  );
};

export default ViewAllAmenities;
