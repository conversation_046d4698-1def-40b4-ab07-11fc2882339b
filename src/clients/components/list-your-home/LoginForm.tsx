'use client';

import { useCallback, useState } from 'react';

import {
  getHomeOwnerProfile,
  getListingAddresses,
  HomeownerProfilePayload,
  ListingAddressesPayload,
} from '@/app/services/listing-addresses';
import Autocomplete, { AutocompleteOption } from '@/clients/ui/autocomplete';
import Button from '@/clients/ui/button';
import { Nullable, ProgressStatus } from '@/types/common';

import { useRouter } from 'next/navigation';

type Props = {};

const LoginForm = ({}: Props) => {
  const router = useRouter();
  const [address, setAdress] = useState<string>('');
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const [options, setOptions] = useState<AutocompleteOption[]>([]);

  const fetchAddresses = useCallback(async (query = '') => {
    setIsFetching(true);
    getListingAddresses<ListingAddressesPayload>(query)
      .then(({ data: { street_address } }) => {
        setOptions(
          street_address.map((_address, index) => ({
            id: index + 1,
            label: _address,
            value: _address,
          })),
        );
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  const onSelectAddress = useCallback((option: AutocompleteOption) => {
    setAdress(option.value);
  }, []);

  const onContinue = useCallback(async () => {
    if (address) {
      const baseNR_URL =
        process.env.NODE_ENV === 'development'
          ? 'https://listing-manager-git-development-nantucket-rentals.vercel.app/listing-manager'
          : 'https://nantucketrentals.com/listing-manager';
      setProgressStatus(ProgressStatus.LOADING);
      const data = await getHomeOwnerProfile<Nullable<HomeownerProfilePayload>>(address);

      if (data?.nrPmoId && (data?.cellphoneNumber || data?.userLoginEmail)) {
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        const isListingBlocked = data.listingStatus === 'blocked' && data.assignedToInternalUser;
        if (isListingBlocked) {
          router.push(
            `${baseNR_URL}/get-started?address=${encodeURIComponent(address)}&nrPropertyId=${
              data.nrPropertyId
            }`,
          );
        } else {
          router.push(
            `${baseNR_URL}/verify?address=${encodeURIComponent(address)}&ivt=${btoa(
              JSON.stringify({
                userId: data.nrPmoId,
                cellphoneNumber: data.cellphoneNumber,
                userLoginEmail: data.userLoginEmail,
              }),
            )}${isListingBlocked ? '&listingBlocked=true' : ''}`,
          );
        }
      } else {
        console.log('no data');
        setProgressStatus(null);
        router.push(`${baseNR_URL}/get-started?address=${encodeURIComponent(address)}`);
      }
    }
  }, [address, router]);

  return (
    <>
      <Autocomplete
        value={address}
        className='text-sm md:text-base w-full placeholder-primary-slate text-primary-slate font-medium px-4 md:px-8 py-4 rounded-[32px]'
        placeholder='Start by entering your property address'
        options={options}
        onChangeValue={(text: string) => setAdress(text)}
        isFetchingData={isFetching}
        fetchData={fetchAddresses}
        onSelect={onSelectAddress}
        dontScroll
      />
      <Button
        className='text-sm py-4 md:py-4 md:px-[36px] font-normal bg-carolina-blue text-white flex m-auto mt-6 w-full rounded-[32px]'
        disabled={progressStatus === ProgressStatus.LOADING}
        onClick={onContinue}
      >
        Continue
      </Button>
    </>
  );
};

export default LoginForm;
