'use client';

import { useCallback, useState } from 'react';

import Button from '@/clients/ui/button';
import Modal from '@/clients/ui/modal';
import Select from '@/clients/ui/select';
import { Nullable } from '@/types/common';

import { useRouter } from 'next/navigation';

const PAYMENT_METHOD_OPTIONS = [
  {
    text: 'ACH',
    value: 'ach',
  },
  {
    text: 'CREDIT CARD',
    value: 'credit_card',
  },
  {
    text: 'None',
    value: 'none',
  },
];

type Props = {
  signature_id: string;
  lease: string;
};

const SelectPaymentMethodDialog = ({ signature_id, lease }: Props) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const router = useRouter();
  const [paymentMethod, setPaymentMethod] = useState<Nullable<'ach' | 'credit_card'>>(null);

  const onChangePaymentMethod = useCallback((val: any) => {
    setPaymentMethod(val);
  }, []);

  const onSubmit = useCallback(() => {
    setIsSubmitting(true);
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/get-sign-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        signature_id,
        lease,
        payment_method: paymentMethod,
      }),
    })
      .then((res) => res.json())
      .then((res) => {
        setIsSubmitting(false);
        if (!res.is_signed) {
          window.location.href = res.sign_url;
        }
      });
  }, [lease, paymentMethod, signature_id]);

  return (
    <Modal open>
      <div className='p-4'>
        <p className='font-medium text-lg'>Select Payment Method</p>
        <hr className='my-2' />
        <Select
          label='Payment method'
          className='w-full h-10 my-2'
          options={PAYMENT_METHOD_OPTIONS}
          value={(paymentMethod as string) ?? ''}
          onChange={onChangePaymentMethod}
        />
        <hr className='my-2' />
        <Button className='w-full' onClick={onSubmit} disabled={isSubmitting}>
          {!isSubmitting ? `Submit` : 'Submitting...'}
        </Button>
      </div>
    </Modal>
  );
};

export default SelectPaymentMethodDialog;
