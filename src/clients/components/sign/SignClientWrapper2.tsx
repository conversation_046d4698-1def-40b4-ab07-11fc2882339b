'use client';

import { useEffect, useState } from 'react';

import BackdropLoader from '@/app/components/common/BackdropLoader';

import { useRouter } from 'next/navigation';

type Props = {
  paramId: string;
};

const SignClientWrapper2 = ({ paramId }: Props) => {
  const router = useRouter();
  const [loading, setIsLoading] = useState<boolean>(true);
  const [isSigned, setIsSigned] = useState<boolean>(false);
  const id = atob(paramId);
  const [sid, lid] = id.split('&').map((item) => item.split('=')[1]);

  useEffect(() => {
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/get-sign-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        signature_id: sid,
        lease: lid,
        payment_method: null,
      }),
    })
      .then((res) => res.json())
      .then((res) => {
        if (res.is_signed) {
          setIsSigned(true);
          setIsLoading(false);
        } else {
          window.location.href = res.sign_url;
        }
      });
  }, [lid, paramId, router, sid]);

  return (
    <div className='flex justify-center py-8'>
      {loading && <BackdropLoader />}
      {isSigned && (
        <div className='w-full'>
          <iframe
            src={`https://docs.google.com/gview?embedded=true&url=${process.env.NEXT_PUBLIC_API_BASE_URL}/get-sign-file?lease=${lid}`}
            width='100%'
            height='800px'
            onLoad={() => setIsLoading(false)}
          ></iframe>
        </div>
      )}
      {sid !== '1' && sid !== '2' && (
        <p className='text-lg'>Something went wrong, please contact Agent</p>
      )}
    </div>
  );
};

export default SignClientWrapper2;
