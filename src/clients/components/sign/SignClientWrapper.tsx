'use client';

import { useEffect, useState } from 'react';

import BackdropLoader from '@/app/components/common/BackdropLoader';

import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';

const SelectPaymentMethodDialog = dynamic(() => import('./SelectPaymentMethodDialog'));

type Props = {
  paramId: string;
};

const SignClientWrapper = ({ paramId }: Props) => {
  const router = useRouter();
  const [isSigned, setIsSigned] = useState<boolean>(false);
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const id = atob(paramId);
  const [sid, lid] = id.split('&').map((item) => item.split('=')[1]);

  useEffect(() => {
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/get-sign-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        signature_id: sid,
        lease: lid,
        payment_method: null,
      }),
    })
      .then((res) => res.json())
      .then((res) => {
        if (res.is_signed) {
          console.log('res is', res);
          setIsSigned(true);
        } else {
          setShowPopup(true);
        }
      });
  }, [lid, paramId, router, sid]);

  return (
    <div className='flex justify-center py-8'>
      {!isSigned && !showPopup && <BackdropLoader />}
      {isSigned && (
        <iframe
          src={`https://docs.google.com/gview?embedded=true&url=${process.env.NEXT_PUBLIC_API_BASE_URL}/get-sign-file?lease=${lid}`}
          width='100%'
          height='800px'
        ></iframe>
      )}
      {sid !== '1' && sid !== '2' && (
        <p className='text-lg'>Something went wrong, please contact Agent</p>
      )}
      {showPopup && !isSigned && <SelectPaymentMethodDialog signature_id={sid} lease={lid} />}
    </div>
  );
};

export default SignClientWrapper;
