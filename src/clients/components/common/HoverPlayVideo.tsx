'use client';

import React, { useRef } from 'react';

import { PlayIcon } from '@heroicons/react/24/outline';

type Props = {
  children: React.ReactNode;
} & React.VideoHTMLAttributes<HTMLVideoElement>;

const HoverPlayVideo = ({ children, ...props }: Props) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const playVideo = () => {
    if (videoRef.current) {
      videoRef.current.play();
    }
  };

  const pauseVideo = () => {
    if (videoRef.current) {
      videoRef.current.pause();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      // Toggle play/pause with Enter or Space key
      event.preventDefault(); // Prevent scrolling when pressing Space
      if (videoRef.current?.paused) {
        playVideo();
      } else {
        pauseVideo();
      }
    }
  };

  return (
    <div
      className="relative w-full h-full"
      role="button" // Adds a semantic role for accessibility
      tabIndex={0} // Makes the div focusable via keyboard navigation
      onMouseEnter={playVideo} // Play video on hover
      onMouseLeave={pauseVideo} // Pause video on mouse leave
      onKeyDown={handleKeyDown}
    >
      <video {...props} ref={videoRef} muted />
      {children}
      <div
        onClick={playVideo}
        role="presentation"
        className="absolute top-5 right-5 w-[60px] h-[60px] rounded-full border border-solid border-white flex items-center justify-center cursor-pointer"
      >
        <PlayIcon className="w-8 h-8 text-white" />
      </div>
    </div>
  );
};

export default HoverPlayVideo;
