'use client';

import { memo, useEffect, useRef } from 'react';

import { Nullable } from '@/types/common';

type Props = {
  playListUrl: string;
};

const BackgroundVideo = ({ playListUrl }: Props) => {
  const videoRef = useRef<Nullable<HTMLVideoElement>>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // ✅ Safari/iOS supports HLS natively – No need for `hls.js`
    // if (video.canPlayType('application/vnd.apple.mpegurl')) {
    //   video.src = playListUrl;
    //   video.load();
    // } else {
    // ✅ Dynamically import `hls.js` only for Chrome, Firefox, Edge
    import('hls.js').then(({ default: Hls }) => {
      if (Hls.isSupported()) {
        const hls = new Hls();
        hls.loadSource(playListUrl);
        hls.attachMedia(video);
      }
    });
    // }
  }, [playListUrl]);

  return (
    <div className='shadow-bg-video relative h-full w-full hidden md:block'>
      <video
        ref={videoRef}
        autoPlay
        loop
        muted
        playsInline
        className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full object-cover'
      />
      <div className='absolute inset-0 video-bg-overlay' />
    </div>
  );
};

export default memo(BackgroundVideo);
