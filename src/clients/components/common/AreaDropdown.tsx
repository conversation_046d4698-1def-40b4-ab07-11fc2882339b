'use client';

import { useState } from 'react';

type Props = {
  selectedIds: string[];
  toggleArea: (area: { id: string; name: string }) => void;
  checkedProperty?: keyof { id: string; name: string };
  dropdownClassName?: string;
};

export const NANTUCKET_AREAS = [
  { id: '1', name: 'Brant Point' },
  { id: '2', name: '<PERSON><PERSON>' },
  { id: '3', name: '<PERSON>' },
  { id: '4', name: '<PERSON><PERSON>' },
  { id: '5', name: 'Edge of Town' },
  { id: '6', name: 'Fishers Landing' },
  { id: '7', name: 'Hummock Pond' },
  { id: '8', name: '<PERSON><PERSON><PERSON>' },
  { id: '9', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: '10', name: 'Miacome<PERSON>' },
  { id: '11', name: 'Mid Island' },
  { id: '12', name: 'Monomo<PERSON>' },
  { id: '13', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { id: '14', name: '<PERSON><PERSON><PERSON>' },
  { id: '15', name: '<PERSON><PERSON><PERSON>' },
  { id: '16', name: '<PERSON><PERSON>' },
  { id: '17', name: '<PERSON><PERSON><PERSON>' },
  { id: '18', name: 'Quid<PERSON>' },
  { id: '19', name: 'Sconset' },
  { id: '20', name: 'Shimmo' },
  { id: '26', name: 'South of Town' },
  { id: '21', name: 'Surfside' },
  { id: '22', name: 'Tom Nevers' },
  { id: '23', name: 'Town' },
  { id: '24', name: 'Wauwinet' },
];

const AreaDropdown = ({
  selectedIds,
  toggleArea,
  checkedProperty = 'id',
  dropdownClassName,
}: Props) => {
  const [areaVisibility, setAreaVisibility] = useState(false);

  const toggleVisibility = () => {
    setAreaVisibility((state) => !state);
  };

  return (
    <div className='dropdown w-full'>
      <label
        tabIndex={0}
        onClick={toggleVisibility}
        className='select flex w-full items-center border-[1px] border-solid border-inherit bg-white'
      >
        Area {selectedIds?.length ? `(${selectedIds?.length})` : ''}
      </label>
      <div
        tabIndex={1}
        className={`w-full menu dropdown-content z-[4] w-80 bg-white p-4 shadow ${
          areaVisibility ? 'block' : 'none hidden h-0'
        }`}
      >
        <div className={`grid grid-cols-2 gap-x-1 gap-y-4 ${dropdownClassName}`}>
          {NANTUCKET_AREAS.map((area) => {
            return (
              <div className='flex items-center' key={area.id}>
                <input
                  type='checkbox'
                  id={area.id}
                  className='area checkbox checkbox-sm mr-2'
                  value={area[checkedProperty]}
                  onChange={() => toggleArea(area)}
                  checked={selectedIds.includes(area[checkedProperty])}
                />
                <label htmlFor={area.id} className='cursor-pointer'>
                  {area.name}
                </label>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AreaDropdown;
