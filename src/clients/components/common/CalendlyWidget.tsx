'use client';

import { memo, useEffect, useRef } from 'react';

const CalendlyWidget = () => {
  const widgetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Add Calendly CSS dynamically
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://assets.calendly.com/assets/external/widget.css';
    document.head.appendChild(link);

    // Load Calendly script
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.head.removeChild(link);
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div
      ref={widgetRef}
      className='calendly-inline-widget'
      data-url='https://calendly.com/liam-nantucketrentals/listingmanagerintro'
      style={{ minWidth: '320px', height: '700px' }}
    />
  );
};

export default memo(CalendlyWidget);
