'use client';

import { SegmentEvents } from '@/types/analytics';

import Link from 'next/link';

const PropertyCardLink = ({
  children,
  href,
  analyticsData,
  className = '',
  ...rest
}: {
  children: React.ReactNode;
  href: string;
  analyticsData: any;
  className?: string;
}) => (
  <Link
    href={href}
    onClick={(e) => {
      window?.analytics?.track(SegmentEvents.PRODUCT_VIEWED, {
        ...analyticsData,
        url: document.URL,
        referrer: document.referrer,
      });
    }}
    className={className}
    {...rest}
  >
    {children}
  </Link>
);

export default PropertyCardLink;
