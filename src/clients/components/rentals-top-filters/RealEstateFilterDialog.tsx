'use client';

import { useContext, useState, useEffect } from 'react';

import { XMarkIcon } from '@heroicons/react/24/outline';

import { RentalAddressesContext } from '@/clients/contexts/RentalAddressesContext';
import Autocomplete from '@/clients/ui/autocomplete';
import Modal from '@/clients/ui/modal';
import { SegmentEvents } from '@/types/analytics';
import { Nullable } from '@/types/common';

import dynamic from 'next/dynamic';
import { useRouter, useSearchParams } from 'next/navigation';

import AreaDropdown from '../common/AreaDropdown';

const BackdropLoader = dynamic(() => import('@/app/components/common/BackdropLoader'), {
  ssr: false,
});

const propertyTypes = [
  'Co-Op',
  'Commercial',
  'Condo',
  'Land',
  'Multi Family',
  'Single Family',
].reverse();

type FormValues = {
  property_type: Nullable<string>;
  address: Nullable<string>;
  lot_size_square_feet_gte: Nullable<string>;
  lot_size_square_feet_lte: Nullable<string>;
  bathroom_num_gte: Nullable<string>;
  bedroom_num_gte: Nullable<string>;
  max_price: Nullable<string>;
  min_price: Nullable<string>;
  area: Nullable<Array<string>>;
  lot_size_acres_gte: Nullable<string>;
  lot_size_acres_lte: Nullable<string>;
  year_built_gte: Nullable<string>;
};

const RealEstateFilterDialog = ({ onClose }: { onClose: () => void }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { addresses, isFetching, fetchAddresses } = useContext(RentalAddressesContext);
  const [isLoading, setIsLoading] = useState(false);

  // Listen for route changes to hide loader and close modal
  useEffect(() => {
    return () => {
      if (isLoading) {
        setIsLoading(false);
        onClose();
      }
    };
  }, [isLoading, onClose]);

  const [formValues, setFormValues] = useState<FormValues>({
    property_type: searchParams.get('property_type'),
    address: searchParams.get('address') ?? null,
    lot_size_square_feet_gte: searchParams.get('lot_size_square_feet_gte'),
    lot_size_square_feet_lte: searchParams.get('lot_size_square_feet_lte'),
    bathroom_num_gte: searchParams.get('bathroom_num_gte'),
    bedroom_num_gte: searchParams.get('bedroom_num_gte'),
    max_price: searchParams.get('max_price'),
    min_price: searchParams.get('min_price'),
    area: searchParams.getAll('area') ?? null,
    lot_size_acres_gte: searchParams.get('lot_size_acres_gte'),
    lot_size_acres_lte: searchParams.get('lot_size_acres_lte'),
    year_built_gte: searchParams.get('year_built_gte'),
  });

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.name;
    const type = e.target.type;
    const value = type === 'checkbox' ? e.target.checked : e.target.value;

    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const onSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const name = e.target.name;
    const value = e.target.value;

    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.ChangeEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('on handle submit');
    setIsLoading(true);

    const output = Object.entries(formValues)
      .filter(([_, value]) => (Array.isArray(value) ? value.length > 0 : value && value !== null))
      .map(([key, value]) =>
        Array.isArray(value)
          ? value.map((val: string) => `${key}=${val}`).join('&')
          : `${key}=${value}`,
      )
      .join('&');

    router.push(`/nantucket-real-estate/?${output}`, { scroll: false });

    // Track analytics after navigation has started
    setTimeout(() => {
      window?.analytics?.track(SegmentEvents.PRODUCT_SEARCHED, {
        action: 'Searched',
        category: 'Real Estates',
        object: 'Product',
        query: {
          address: formValues.address ?? '',
          area: formValues.area ?? [],
          bedroom_num_gte: formValues.bedroom_num_gte,
          min_price: formValues.min_price,
          max_price: formValues.max_price,
          property_type: formValues.property_type,
        },
        region: 'Massachusetts',
        city: 'Nantucket',
        country: 'United States',
        url: document.URL,
        referrer: document.referrer,
      });
    }, 0);
  };

  const toggleArea = ({ id, name }: { id: string; name: string }) => {
    setFormValues((form: FormValues) => {
      const area = formValues?.area ?? [];
      const hasId = formValues.area?.includes(name);
      return {
        ...form,
        area: hasId ? area?.filter((area) => area !== name) : area.concat(name),
      };
    });
  };

  const onClear = () => {
    setFormValues({
      property_type: null,
      address: null,
      lot_size_square_feet_gte: null,
      lot_size_square_feet_lte: null,
      bathroom_num_gte: null,
      bedroom_num_gte: null,
      max_price: null,
      min_price: null,
      area: null,
      year_built_gte: null,
      lot_size_acres_gte: null,
      lot_size_acres_lte: null,
    });
    router.push('/nantucket-real-estate');
  };

  return (
    <div>
      {isLoading && <BackdropLoader />}
      <Modal
        open={true}
        onClose={onClose}
        className='md:rounded-md min-h-[100vh] md:min-h-full md:w-1/2'
      >
        <div className='px-4 md:px-5 py-5'>
          <div className='flex items-center justify-between mb-5'>
            <p className='text-2xl font-medium leading-[34px]'>Search Filters</p>
            <XMarkIcon onClick={onClose} className='w-5 h-5 cursor-pointer text-gray-main' />
          </div>
          <form id='formFilter' onSubmit={handleSubmit}>
            <div className='flex flex-col gap-y-2'>
              <div className='flex flex-col justify-between gap-y-2 md:flex-row md:gap-x-2'>
                <select
                  className='select select-bordered w-full bg-white'
                  name='min_price'
                  onChange={onSelectChange}
                  value={formValues?.min_price ?? ''}
                >
                  <option value=''>$ Min Price</option>
                  <option value=''>Any</option>
                  <option value='500000'>$ 500K</option>
                  <option value='1000000'>$ 1M</option>
                  <option value='2000000'>$ 2M</option>
                  <option value='3000000'>$ 3M</option>
                  <option value='4000000'>$ 4M</option>
                  <option value='5000000'>$ 5M</option>
                  <option value='7500000'>$ 7.5M</option>
                  <option value='10000000'>$ 10M</option>
                  <option value='15000000'>$ 15M</option>
                  <option value='20000000'>$ 20M</option>
                </select>
                <select
                  className='select select-bordered w-full bg-white'
                  name='max_price'
                  onChange={onSelectChange}
                  value={formValues?.max_price ?? ''}
                >
                  <option value=''>$ Max Price</option>
                  <option value=''>Any</option>
                  <option value='1000000'>$ 1M</option>
                  <option value='2000000'>$ 2M</option>
                  <option value='3000000'>$ 3M</option>
                  <option value='4000000'>$ 4M</option>
                  <option value='5000000'>$ 5M</option>
                  <option value='7500000'>$ 7.5M</option>
                  <option value='10000000'>$ 10M</option>
                  <option value='15000000'>$ 15M</option>
                  <option value='20000000'>$ 20M</option>
                  <option value='30000000'>$ 30M</option>
                </select>
              </div>
              <div className='flex flex-col gap-y-2 md:flex-row md:justify-between md:gap-x-2'>
                <select
                  className='select select-bordered w-full md:w-[calc(100%-3.5rem)] bg-white'
                  name='property_type'
                  onChange={onSelectChange}
                  value={formValues?.property_type ?? ''}
                >
                  <option value=''>Property Type</option>
                  {propertyTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                <div className='w-full'>
                  <Autocomplete
                    className='font-semibold'
                    options={addresses.map((_address, index) => ({
                      id: index + 1,
                      label: _address,
                      value: _address,
                    }))}
                    value={formValues.address}
                    onChangeValue={(text: string) =>
                      setFormValues({
                        ...formValues,
                        address: text,
                      })
                    }
                    onSelect={(option) =>
                      setFormValues({
                        ...formValues,
                        address: option.value,
                      })
                    }
                    isFetchingData={isFetching}
                    fetchData={fetchAddresses}
                  />
                </div>
              </div>
              <div className='flex flex-col gap-y-2 md:flex-row md:justify-between md:gap-x-2'>
                <AreaDropdown
                  toggleArea={toggleArea}
                  selectedIds={formValues?.area ?? []}
                  checkedProperty='name'
                  dropdownClassName='grid-cols-2 md:grid-cols-3'
                />
              </div>
              <div className='flex flex-col justify-between gap-y-2 md:flex-row md:gap-x-2'>
                <select
                  className='select select-bordered w-full bg-white'
                  name='bedroom_num_gte'
                  onChange={onSelectChange}
                  value={formValues?.bedroom_num_gte ?? ''}
                >
                  <option value=''>Min Bedrooms</option>
                  <option value='1'>1+ bedroom</option>
                  <option value='2'>2+ bedrooms</option>
                  <option value='3'>3+ bedrooms</option>
                  <option value='4'>4+ bedrooms</option>
                  <option value='5'>5+ bedrooms</option>
                  <option value='6'>6+ bedrooms</option>
                  <option value='7'>7+ bedrooms</option>
                  <option value='8'>8+ bedrooms</option>
                </select>
                <select
                  className='select select-bordered w-full bg-white'
                  name='lot_size_square_feet_gte'
                  onChange={onSelectChange}
                  value={formValues?.lot_size_square_feet_gte ?? ''}
                >
                  <option value=''>Min Living Area</option>
                  <option value=''>No Min</option>
                  <option value='500'>500+ square feet</option>
                  <option value='1000'>1,000+ square feet</option>
                  <option value='1500'>1,500+ square feet</option>
                  <option value='2000'>2,000+ square feet</option>
                  <option value='2500'>2,500+ square feet</option>
                  <option value='3000'>3,000+ square feet</option>
                  <option value='3500'>3,500+ square feet</option>
                  <option value='4000'>4,000+ square feet</option>
                  <option value='5000'>5,000+ square feet</option>
                  <option value='7500'>7,500+ square feet</option>
                  <option value='10000'>10,000+ square feet</option>
                </select>
              </div>
              <div className='flex flex-col justify-between gap-y-2 md:flex-row md:gap-x-2'>
                <select
                  className='select select-bordered w-full bg-white'
                  name='lot_size_acres_gte'
                  onChange={onSelectChange}
                  value={formValues?.lot_size_acres_gte ?? ''}
                >
                  <option value=''>Min Lot Size</option>
                  <option value=''>No Min</option>
                  <option value='0.125'>5,000+ square feet</option>
                  <option value='0.250'>10,000+ square feet</option>
                  <option value='0.500'>20,000+ square feet</option>
                  <option value='1.000'>1+ acres</option>
                  <option value='5.000'>5+ acres</option>
                </select>
                <select
                  className='select select-bordered w-full bg-white'
                  name='year_built_gte'
                  onChange={onSelectChange}
                  value={formValues?.year_built_gte ?? ''}
                >
                  <option value=''>Min Year Built</option>
                  <option value=''>any</option>
                  <option value='1900'>1900</option>
                  <option value='1910'>1910</option>
                  <option value='1920'>1920</option>
                  <option value='1930'>1930</option>
                  <option value='1940'>1940</option>
                  <option value='1950'>1950</option>
                  <option value='1960'>1960</option>
                  <option value='1970'>1970</option>
                  <option value='1980'>1980</option>
                  <option value='1990'>1990</option>
                  <option value='2000'>2000</option>
                  <option value='2010'>2010</option>
                  <option value='2020'>2020</option>
                </select>
              </div>
              <div className='modal-action mt-2'>
                <div className='grid w-full grid-cols-1 gap-2 md:grid-cols-2'>
                  <button onClick={onClear} className='btn rounded-none bg-slate-50'>
                    Clear Filters
                  </button>
                  <button
                    className='btn btn-primary rounded-none bg-cyan-900 border-cyan-900 hover:border-cyan-700 hover:bg-cyan-700 text-white'
                    id='filterButton'
                    type='submit'
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
};

export default RealEstateFilterDialog;
