'use client';

import { useContext, useState, useEffect } from 'react';

import { XMarkIcon } from '@heroicons/react/24/outline';

import { RentalAddressesContext } from '@/clients/contexts/RentalAddressesContext';
import Autocomplete from '@/clients/ui/autocomplete';
import Modal from '@/clients/ui/modal';
import { SegmentEvents } from '@/types/analytics';
import { Nullable } from '@/types/common';

import dayjs from 'dayjs';
import dynamic from 'next/dynamic';
import { useSearchParams, useRouter } from 'next/navigation';

import AreaDropdown, { NANTUCKET_AREAS } from '../common/AreaDropdown';
import DateRangePicker from '../date-range-picker';

const BackdropLoader = dynamic(() => import('@/app/components/common/BackdropLoader'), {
  ssr: false,
});

type FormValues = {
  start_date: Nullable<string>;
  end_date: Nullable<string>;
  nights: Nullable<string>;
  checkin: Nullable<string>;
  checkin_date: Nullable<string>;
  address: Nullable<string>;
  guests: Nullable<string>;
  bedroom_num_gte: Nullable<string>;
  max_price: Nullable<string>;
  min_price: Nullable<string>;
  area: Nullable<Array<string>>;
  waterfront: Nullable<boolean>;
  water_view: Nullable<boolean>;
  air_conditioning: Nullable<boolean>;
  pool: Nullable<boolean>;
  pet_allow: Nullable<boolean>;
};

function addHours(date: Date, hours: number) {
  date.setHours(date.getHours() + hours);

  return date;
}

function addDays(date: Date, days: number) {
  var result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

type Props = {
  onClose: () => void;
};

const RentalsFilterDialog = ({ onClose }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { addresses, isFetching, fetchAddresses } = useContext(RentalAddressesContext);
  const [isLoading, setIsLoading] = useState(false);

  // Listen for route changes to hide loader and close modal
  useEffect(() => {
    return () => {
      if (isLoading) {
        setIsLoading(false);
        onClose();
      }
    };
  }, [isLoading, onClose]);

  const [formValues, setFormValues] = useState<FormValues>({
    nights: searchParams.get('nights'),
    checkin: searchParams.get('checkin') || '6',
    checkin_date: searchParams.get('checkin_date'),
    start_date: searchParams.get('start_date'),
    end_date: searchParams.get('end_date'),
    address: searchParams.get('address') ?? null,
    guests: searchParams.get('guests'),
    bedroom_num_gte: searchParams.get('bedroom_num_gte'),
    max_price: searchParams.get('max_price'),
    min_price: searchParams.get('min_price'),
    area: searchParams.getAll('area') ?? null,
    air_conditioning: searchParams.get('air_conditioning') === 'true',
    waterfront: searchParams.get('waterfront') === 'true',
    water_view: searchParams.get('water_view') === 'true',
    pool: searchParams.get('pool') === 'true',
    pet_allow: searchParams.get('pet_allow') === 'true',
  });

  const startDate = formValues.checkin_date ? addHours(new Date(formValues.checkin_date), 6) : null;
  const addDate = formValues?.nights ? parseInt(formValues?.nights) : 7;
  const endDate = startDate ? addDays(startDate, addDate) : null;

  const isWeekday = (date: Date) => {
    const day = date.getDay();
    if (formValues.checkin == '-1') return true;
    else {
      return day === (formValues.checkin ? parseInt(formValues.checkin) : -1);
    }
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.name;
    const type = e.target.type;
    const value = type === 'checkbox' ? e.target.checked : e.target.value;

    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const onDateChange = ([start]: [Date]) => {
    setFormValues({
      ...formValues,
      checkin_date: start.toISOString().split('T')[0],
    });
  };

  const onSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const name = e.target.name;
    const value = e.target.value;

    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.ChangeEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const output = Object.entries(formValues)
      .filter(([_, value]) => (Array.isArray(value) ? value.length > 0 : value && value !== null))
      .map(([key, value]) =>
        Array.isArray(value)
          ? value.map((val: string) => `${key}=${val}`).join('&')
          : `${key}=${value}`,
      )
      .join('&');

    // Navigate immediately with scroll: false for better performance
    router.push(`/nantucket-rentals/?${output}`, { scroll: false });

    // Track analytics after navigation has started
    setTimeout(() => {
      window?.analytics?.track(SegmentEvents.PRODUCT_SEARCHED, {
        action: 'Searched',
        category: 'Rental Properties',
        object: 'Product',
        query: {
          address: formValues.address ?? '',
          air_conditioning: formValues.air_conditioning,
          area:
            formValues.area?.map((_a) => NANTUCKET_AREAS.find((_area) => _area.id === _a)?.name) ??
            [],
          bedroom_num_gte: formValues.bedroom_num_gte,
          startDate: !!startDate ? dayjs(startDate).format('YYYY-MM-DD') : null,
          endDate: !!endDate ? dayjs(endDate).format('YYYY-MM-DD') : null,
          limit: formValues.guests,
          min_price: formValues.min_price,
          max_price: formValues.max_price,
          nights: !!startDate && !!endDate ? dayjs(endDate).diff(startDate, 'days') : null,
          pet_allow: formValues.pet_allow,
          pool: formValues.pool,
          water_view: formValues.water_view,
          waterfront: formValues.waterfront,
        },
        region: 'Massachusetts',
        city: 'Nantucket',
        country: 'United States',
        url: document.URL,
        referrer: document.referrer,
      });
    }, 0);
  };

  const toggleArea = ({ id, name }: { id: string; name: string }) => {
    setFormValues((form: FormValues) => {
      const area = formValues.area ?? [];
      const hasId = formValues.area?.includes(id);

      return {
        ...form,
        area: hasId ? area?.filter((area) => area !== id) : area.concat(id),
      };
    });
  };

  const onClear = () => {
    setFormValues({
      start_date: null,
      end_date: null,
      nights: null,
      checkin: '6',
      checkin_date: null,
      address: null,
      guests: null,
      bedroom_num_gte: null,
      max_price: null,
      min_price: null,
      area: null,
      air_conditioning: false,
      waterfront: false,
      water_view: false,
      pool: false,
      pet_allow: false,
    });
    router.push('/nantucket-rentals');
  };

  return (
    <div>
      {isLoading && <BackdropLoader />}
      <Modal
        open={true}
        onClose={onClose}
        className='md:rounded-md min-h-[100vh] md:min-h-full lg:w-1/2'
      >
        <div className='px-4 md:px-5 py-5'>
          <div className='flex items-center justify-between mb-5'>
            <p className='text-2xl font-medium leading-[34px]'>Search Filters</p>
            <XMarkIcon onClick={onClose} className='w-5 h-5 cursor-pointer text-gray-main' />
          </div>
          <form id='formFilter' onSubmit={handleSubmit}>
            <div className='flex flex-col gap-y-2'>
              <div className='flex flex-col gap-y-2 md:flex-row md:justify-between md:gap-x-2'>
                <select
                  className='select select-bordered w-full bg-white'
                  name='nights'
                  onChange={onSelectChange}
                  value={formValues?.nights ?? ''}
                >
                  <option value=''># of Weeks: 1</option>
                  <option value='7'>1 Week</option>
                  <option value='14'>2 Weeks</option>
                  <option value='21'>3 Weeks</option>
                  <option value='28'>4 Weeks</option>
                  <option value='35'>5 Weeks</option>
                </select>
                <select
                  className='select select-bordered w-full bg-white'
                  name='checkin'
                  onChange={onSelectChange}
                  value={formValues?.checkin ?? ''}
                >
                  <option value='6'>Check-in: Saturday</option>
                  <option value='0'>Check-in: Sunday</option>
                  <option value='-1'>Check-in: Any</option>
                </select>
              </div>

              <div className='flex flex-col gap-y-2 md:flex-row md:justify-between md:gap-x-2'>
                <div className='w-full xl:hidden'>
                  <DateRangePicker
                    className='mb-0 font-semibold text-slate-900'
                    value={[startDate, endDate]}
                    onChange={onDateChange}
                    isMobile
                    filterDate={isWeekday}
                  />
                </div>
                <div className='hidden xl:block w-full'>
                  <DateRangePicker
                    className='mb-0 font-semibold text-slate-900'
                    value={[startDate, endDate]}
                    onChange={onDateChange}
                    filterDate={isWeekday}
                  />
                </div>
                <div className='w-full'>
                  <Autocomplete
                    className='font-semibold'
                    options={addresses.map((_address, index) => ({
                      id: index + 1,
                      label: _address,
                      value: _address,
                    }))}
                    value={formValues.address}
                    onChangeValue={(text: string) =>
                      setFormValues({
                        ...formValues,
                        address: text,
                      })
                    }
                    onSelect={(option) =>
                      setFormValues({
                        ...formValues,
                        address: option.value,
                      })
                    }
                    isFetchingData={isFetching}
                    fetchData={fetchAddresses}
                  />
                </div>
              </div>
              <div className='flex flex-col gap-y-2 md:flex-row md:justify-between md:gap-x-2'>
                <AreaDropdown
                  toggleArea={toggleArea}
                  selectedIds={formValues?.area ?? []}
                  dropdownClassName='grid-cols-2 md:grid-cols-3'
                />
              </div>

              <div className='flex flex-col justify-between gap-y-2 md:flex-row md:gap-x-2'>
                <select
                  className='select select-bordered w-full bg-white'
                  name='guests'
                  onChange={onSelectChange}
                  value={formValues?.guests ?? ''}
                >
                  <option value=''>Guests</option>
                  <option value='1'>1 Guest</option>
                  <option value='2'>2 Guests</option>
                  <option value='3'>3 Guests</option>
                  <option value='4'>4 Guests</option>
                  <option value='5'>5 Guests</option>
                  <option value='6'>6 Guests</option>
                  <option value='7'>7 Guests</option>
                  <option value='8'>8+ Guests</option>
                </select>

                <select
                  className='select select-bordered w-full bg-white'
                  name='bedroom_num_gte'
                  onChange={onSelectChange}
                  value={formValues?.bedroom_num_gte ?? ''}
                >
                  <option value=''>Bedrooms</option>
                  <option value='1'>1+ bedroom</option>
                  <option value='2'>2+ bedrooms</option>
                  <option value='3'>3+ bedrooms</option>
                  <option value='4'>4+ bedrooms</option>
                  <option value='5'>5+ bedrooms</option>
                  <option value='6'>6+ bedrooms</option>
                  <option value='7'>7+ bedrooms</option>
                  <option value='8'>8+ bedrooms</option>
                </select>
              </div>

              <div className='flex flex-col justify-between gap-y-2 md:flex-row md:gap-x-2'>
                <select
                  className='select select-bordered w-full bg-white'
                  name='min_price'
                  onChange={onSelectChange}
                  value={formValues?.min_price ?? ''}
                >
                  <option value=''>$ Min Weekly</option>
                  <option value=''>No Min</option>
                  <option value='5000'>$ 5,000</option>
                  <option value='7500'>$ 7,500</option>
                  <option value='10000'>$ 10,000</option>
                  <option value='12500'>$ 12,500</option>
                  <option value='15000'>$ 15,000</option>
                  <option value='17500'>$ 17,500</option>
                  <option value='20000'>$ 20,000</option>
                </select>
                <select
                  className='select select-bordered w-full bg-white'
                  name='max_price'
                  onChange={onSelectChange}
                  value={formValues?.max_price ?? ''}
                >
                  <option value=''>$ Max Weekly</option>
                  <option value=''>No Max</option>
                  <option value='5000'>$ 5,000</option>
                  <option value='7500'>$ 7,500</option>
                  <option value='10000'>$ 10,000</option>
                  <option value='12500'>$ 12,500</option>
                  <option value='15000'>$ 15,000</option>
                  <option value='17500'>$ 17,500</option>
                  <option value='20000'>$ 20,000</option>
                  <option value='25000'>$ 25,000</option>
                  <option value='30000'>$ 30,000</option>
                </select>
              </div>

              <div>
                <h4 className='pb-2 font-semibold'>Amenities</h4>
                <div className='flex flex-col gap-y-2'>
                  <label className='flex items-center'>
                    <input
                      type='checkbox'
                      className='checkbox checkbox-sm mr-2'
                      name='air_conditioning'
                      onChange={onInputChange}
                      checked={formValues?.air_conditioning === true}
                    />
                    A/C
                  </label>

                  <label className='flex items-center'>
                    <input
                      type='checkbox'
                      className='checkbox checkbox-sm mr-2'
                      name='waterfront'
                      onChange={onInputChange}
                      checked={formValues?.waterfront === true}
                    />
                    Waterfront
                  </label>

                  <label className='flex items-center'>
                    <input
                      type='checkbox'
                      className='checkbox checkbox-sm mr-2'
                      name='pool'
                      onChange={onInputChange}
                      checked={formValues?.pool === true}
                    />
                    Pool
                  </label>

                  <label className='flex items-center'>
                    <input
                      type='checkbox'
                      className='checkbox checkbox-sm mr-2'
                      name='water_view'
                      onChange={onInputChange}
                      checked={formValues?.water_view === true}
                    />
                    WaterView
                  </label>

                  <label className='flex items-center'>
                    <input
                      type='checkbox'
                      className='checkbox checkbox-sm mr-2'
                      name='pet_allow'
                      onChange={onInputChange}
                      checked={formValues?.pet_allow === true}
                    />
                    Pet Friendly
                  </label>
                </div>
              </div>

              <div className='modal-action mt-2'>
                <div className='grid w-full grid-cols-1 gap-2 md:grid-cols-2'>
                  <button onClick={onClear} className='btn rounded-none bg-slate-50'>
                    Clear Filters
                  </button>
                  <button
                    className='btn btn-primary rounded-none bg-cyan-900 border-cyan-900 hover:border-cyan-700 hover:bg-cyan-700 text-white'
                    id='filterButton'
                    type='submit'
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
};

export default RentalsFilterDialog;
