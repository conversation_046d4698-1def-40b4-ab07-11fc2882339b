'use client';

import { useState } from 'react';

import { Icon } from '@/clients/components/icon';

import Link from 'next/link';

import { PropertyCard } from '../../../app/components/real-estate/PropertyCard';

export type RealEstateFilterType = 'new' | 'ua' | 'recent' | 'price';

type ListSearchResult = {
  results: any[];
};

type RealEstatePreviewProps = {
  listMap: Record<RealEstateFilterType, ListSearchResult>;
};

type Tab = { id: RealEstateFilterType; text: string };

export const RealEstatePreview = ({ listMap }: RealEstatePreviewProps) => {
  const [activeTab, setActiveTab] = useState<RealEstateFilterType>('new');

  const tabs: Tab[] = [
    { id: 'new', text: 'New Listings' },
    { id: 'price', text: 'Price Changes' },
    { id: 'ua', text: 'Under Agreement' },
    { id: 'recent', text: 'Recent Sales' },
  ];

  const onTabChange = (tab: RealEstateFilterType) => {
    setActiveTab(tab);
  };

  const realEstateList = listMap[activeTab];

  return (
    <main className='container pr-0 pt-12 pb-14 xl:py-20'>
      <div>
        <h2 className='text-2xl xl:text-4xl font-semibold text-center'>
          Nantucket Real Estate Listings
        </h2>
      </div>
      <div className='my-5 md:my-7'>
        <ul className='flex flex-nowrap overflow-auto no-scrollbar lg:justify-center'>
          {tabs.map((tab, i) => {
            const isFirst = i === 0;
            const activeClass = tab.id === activeTab ? 'border-navy-blue' : 'border-[#D8E2E4]';
            return (
              <li
                key={tab.id}
                className={`whitespace-nowrap cursor-pointer pb-5 border-b-2 border-solid ${activeClass}`}
                onClick={() => onTabChange(tab.id)}
              >
                <span className={`${!isFirst ? 'px-6' : 'pr-6'}`}>{tab.text}</span>
              </li>
            );
          })}
        </ul>
      </div>
      <div className='flex flex-nowrap overflow-auto pb-1 mb-7 md:flex-wrap md:pb-10 md:overflow-hidden md:max-w-[550px] md:mx-auto md:justify-between md:grid-cols md:grid-cols-3 lg:max-w-none lg:grid lg:grid-cols-3 lg:gap-x-7 lg:gap-y-7 xl:grid-cols-4 no-scrollbar'>
        {realEstateList?.results?.map((property: any) => (
          <div className='pr-4 md:pr-0 lg:flex lg:justify-center' key={property.link_id}>
            <PropertyCard
              key={property.link_id}
              property={property}
              className='shadow-md min-w-[255px]'
            />
          </div>
        ))}
      </div>
      <Link href='/nantucket-real-estate' className='block mx-auto w-80'>
        <button className='h-14 px-10 py-4 rounded bg-[#4C737F] text-white'>
          <span className='flex items-center gap-x-1'>
            View All Real Estate Listings
            <Icon icon='arrow-right-inverse' height={19} width={19} />
          </span>
        </button>
      </Link>
    </main>
  );
};
