import React from 'react';

import classNames from 'classnames';

type Props = {
  isLeased?: boolean;
  isOwnerTime?: boolean;
  isInverted?: boolean;
};

const Triangle = ({ isLeased, isOwnerTime, isInverted }: Props) => {
  return (
    <div
      className={classNames('absolute inset-0 z-[1] h-full', {
        '[clip-path:polygon(100%_100%,100%_0,0%_100%)]': !isInverted,
        '[clip-path:polygon(0_0,100%_0,0%_100%)]': isInverted,
        'bg-primary': isLeased,
        'bg-gray-100': isOwnerTime,
      })}
    />
  );
};

export default Triangle;
