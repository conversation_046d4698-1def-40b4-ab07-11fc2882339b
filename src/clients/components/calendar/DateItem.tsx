import React, { memo } from 'react';

import { IListingAvailability, ListingAvailabilityType } from '@/types/rental-listing-details';
import { formatAvailabilityDateString } from '@/utils/availability';
import { isBetweenTwoDates } from '@/utils/calendar';

import classNames from 'classnames';
import dayjs from 'dayjs';
import { twMerge } from 'tailwind-merge';

import Triangle from './Triangle';

type Props = {
  dateObject: dayjs.Dayjs;
  date: number;
  availableRanges?: IListingAvailability[];
  active?: boolean;
  insideCurrentMonth?: boolean;
  className?: string;
};

const DateItem = ({
  date,
  className = '',
  insideCurrentMonth,
  availableRanges = [],
  dateObject,
}: Props) => {
  const bookedRanges = availableRanges.filter(
    ({ from_date, to_date, type }) =>
      isBetweenTwoDates(from_date, to_date, dateObject) && type === ListingAvailabilityType.LEASED,
  );
  const blockedRanges = availableRanges.filter(
    ({ from_date, to_date, type }) =>
      isBetweenTwoDates(from_date, to_date, dateObject) && type !== ListingAvailabilityType.LEASED,
  );

  const [
    [isBooked, bookedIsBeginning, bookedIsEnd],
    [isBlocked, blockedIsBeginning, blockedIsEnd],
  ] = [bookedRanges, blockedRanges].map((subRanges) => {
    const len = subRanges.length;
    const { from_date, to_date } = subRanges[0] || {};
    const start = dateObject.isSame(formatAvailabilityDateString(from_date));
    const end = dateObject.isSame(formatAvailabilityDateString(to_date));
    const isBeginning = !(len <= 1 && end);
    const isEnd = !(len <= 1 && start);
    return [len > 0, isBeginning, isEnd];
  });

  return (
    <div
      className={twMerge(
        classNames('relative flex items-center justify-center py-2.5 text-xs leading-[150%]', {
          'text-disabled': !insideCurrentMonth,
        }),
        className,
      )}
    >
      <span className='z-[2]'>{date}</span>
      {insideCurrentMonth && (
        <>
          {isBlocked && blockedIsEnd && <Triangle isInverted={blockedIsEnd} isOwnerTime />}
          {isBooked && bookedIsEnd && <Triangle isInverted={bookedIsEnd} isLeased />}
          {isBlocked && blockedIsBeginning && <Triangle isOwnerTime />}
          {isBooked && bookedIsBeginning && <Triangle isLeased />}
        </>
      )}
    </div>
  );
};

export default memo(DateItem);
