import React, { useMemo } from 'react';

import { IListingAvailability, IListingRate } from '@/types/rental-listing-details';
import { getAvailabitiesForCalendar, getRatesForCalendar } from '@/utils/availability';
import { getFirstDayOfMonth, getNumberOfDaysInMonth, isBetweenTwoDates } from '@/utils/calendar';
import { currencyFormatterRound } from '@/utils/common';

import dayjs from 'dayjs';

import DateItem from './DateItem';

export const TOTAL_SLOTS_IN_CALENDAR = 42;

type Props = {
  month: number;
  year: number;
  rateRanges?: IListingRate[];
  availableRanges?: IListingAvailability[];
};

const MONTHS = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
const DAY_NAMES = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const Calendar = ({ month, year, rateRanges = [], availableRanges = [] }: Props) => {
  const numberOfDaysInMonth = useMemo(() => getNumberOfDaysInMonth(year, month), [year, month]);
  const firstDayOfMonth = useMemo(() => getFirstDayOfMonth(year, month), [year, month]);
  const numberOfDaysLastMonth = useMemo(
    () =>
      month > 0 ? getNumberOfDaysInMonth(year, month - 1) : getNumberOfDaysInMonth(year - 1, 11),
    [year, month],
  );

  const rates = useMemo(
    () =>
      getRatesForCalendar(
        rateRanges,
        year,
        month,
        numberOfDaysLastMonth,
        numberOfDaysInMonth,
        firstDayOfMonth,
      ),
    [firstDayOfMonth, month, numberOfDaysInMonth, numberOfDaysLastMonth, rateRanges, year],
  );

  const availabilities = useMemo(
    () =>
      getAvailabitiesForCalendar(
        availableRanges,
        year,
        month,
        numberOfDaysLastMonth,
        numberOfDaysInMonth,
        firstDayOfMonth,
      ),
    [availableRanges, firstDayOfMonth, month, numberOfDaysInMonth, numberOfDaysLastMonth, year],
  );

  const totalSlots = useMemo(
    () =>
      firstDayOfMonth +
      numberOfDaysInMonth +
      ((TOTAL_SLOTS_IN_CALENDAR - firstDayOfMonth - numberOfDaysInMonth) % 7),
    [firstDayOfMonth, numberOfDaysInMonth],
  );

  return (
    <div className='md:mx-4'>
      <p className='w-full text-center text-sm font-medium leading-[120%]'>
        {MONTHS[month]} {year}
      </p>
      <div className='mt-2 flex'>
        <div className='grid flex-grow grid-cols-7 gap-0'>
          {DAY_NAMES.map((day) => (
            <div
              key={day}
              className='flex items-center justify-center py-[5px] text-xs leading-[150%]'
            >
              {day}
            </div>
          ))}
          {Array(firstDayOfMonth)
            .fill(0)
            .map((_, index) => (
              <DateItem
                key={`${MONTHS[month - 1]}/${year}- ${
                  index + (numberOfDaysLastMonth - firstDayOfMonth) + 1
                }`}
                date={index + (numberOfDaysLastMonth - firstDayOfMonth) + 1}
                dateObject={dayjs(
                  `${year}/${month}/${index + (numberOfDaysLastMonth - firstDayOfMonth) + 1}`,
                  'YYYY/M/D',
                )}
                availableRanges={availabilities}
              />
            ))}
          {Array(numberOfDaysInMonth)
            .fill(0)
            .map((_, index) => (
              <DateItem
                key={`${MONTHS[month]}/${year}- ${index + 1}`}
                date={index + 1}
                dateObject={dayjs(`${year}/${month + 1}/${index + 1}`, 'YYYY/M/D')}
                availableRanges={availabilities}
                insideCurrentMonth
              />
            ))}
          {Array((TOTAL_SLOTS_IN_CALENDAR - firstDayOfMonth - numberOfDaysInMonth) % 7)
            .fill(0)
            .map((_, index) => (
              <DateItem
                key={`${MONTHS[month + 1]}/${year}- ${index + 1}`}
                date={index + 1}
                dateObject={dayjs(`${year}/${month + 1}/${index + 1}`, 'YYYY/M/D').add(1, 'month')}
                availableRanges={availabilities}
              />
            ))}
        </div>
        <div>
          <div className='flex min-w-[100px] items-start justify-center py-[5px] text-xs leading-[150%]'>
            Rent
          </div>
          {Array(totalSlots / 7)
            .fill(0)
            .map((_, index) => {
              let day = index * 7 - firstDayOfMonth;
              if (day < 0) {
                day = 0;
              }
              const date = dayjs(`${year}/${month + 1}/${day + 1}`, 'YYYY/M/D');
              const range = rates.find(({ from_date, to_date }) =>
                isBetweenTwoDates(from_date, to_date, date),
              );
              return (
                <div
                  key={index}
                  className='flex min-w-[70px] items-start justify-center bg-light-gray py-2.5 text-xs leading-[150%]'
                >
                  {range?.weekly_amount ? (
                    currencyFormatterRound.format(Number(range.weekly_amount))
                  ) : (
                    <a href='tel:5083255000'>Call for Rate</a>
                  )}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default Calendar;
