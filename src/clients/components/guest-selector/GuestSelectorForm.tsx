import React, { useCallback } from 'react';

import { MinusCircleIcon, PlusCircleIcon, UserIcon } from '@heroicons/react/24/outline';

import FormHelperText from '@/app/ui/form-helper-text';

import { GuestType, GuestsValues } from '.';

type Props = {
  guestCount: GuestsValues;
  onChange: (count: GuestsValues) => void;
  onClose: () => void;
  capacity?: number;
};

const GuestSelectorForm = ({ guestCount, onChange, onClose, capacity = 1 }: Props) => {
  const onChangeCount = useCallback(
    (count: number, type: GuestType) => {
      onChange({
        ...guestCount,
        adults: type === GuestType.ADULTS ? count : guestCount.adults,
        children: type === GuestType.CHILDREN ? count : guestCount.children,
      });
    },
    [guestCount, onChange],
  );
  return (
    <div className='py-4 px-3 flex flex-col' onClick={(e) => e.stopPropagation()}>
      <div className='flex flex-row items-center justify-between my-2 font-medium' tabIndex={0}>
        <span>Adults</span>
        <div className='flex items-center justify-between w-[100px] gap-3'>
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (guestCount.adults > 1) {
                onChangeCount(guestCount.adults - 1, GuestType.ADULTS);
              }
            }}
            className={`${guestCount.adults === 1 && 'text-disabled cursor-not-allowed'}`}
          >
            <MinusCircleIcon className='w-[30px] h-[30px]' />
          </button>
          {guestCount.adults}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onChangeCount(guestCount.adults + 1, GuestType.ADULTS);
            }}
          >
            <PlusCircleIcon className='w-[30px] h-[30px]' />
          </button>
        </div>
      </div>
      <div className='flex flex-row items-center justify-between my-2 font-medium' tabIndex={1}>
        <span>Children</span>
        <div className='flex items-center justify-between w-[100px] gap-3'>
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (guestCount.children > 0) {
                onChangeCount(guestCount.children - 1, GuestType.CHILDREN);
              }
            }}
            className={`${guestCount.children === 0 && 'text-disabled cursor-not-allowed'}`}
          >
            <MinusCircleIcon className='w-[30px] h-[30px]' />
          </button>

          {guestCount.children}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onChangeCount(guestCount.children + 1, GuestType.CHILDREN);
            }}
          >
            <PlusCircleIcon className='w-[30px] h-[30px]' />
          </button>
        </div>
      </div>
      {guestCount?.adults + guestCount.children > capacity && (
        <FormHelperText className='my-2' error>
          Maximum guests for this property is {capacity}
        </FormHelperText>
      )}
      <span className='self-end underline font-medium my-2' onClick={onClose}>
        Close
      </span>
    </div>
  );
};

export default GuestSelectorForm;
