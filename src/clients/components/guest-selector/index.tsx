'use client';

import React, { useState } from 'react';

import { UserIcon } from '@heroicons/react/24/outline';

import Dropdown from '@/app/ui/dropdown';

import classNames from 'classnames';
import dynamic from 'next/dynamic';

const GuestSelectorForm = dynamic(() => import('./GuestSelectorForm'), {
  ssr: false,
});

export type GuestsValues = {
  adults: number;
  children: number;
};

export enum GuestType {
  ADULTS = 'ADULTS',
  CHILDREN = 'CHILDREN',
}

type Props = {
  guestCount: GuestsValues;
  onChange: (count: GuestsValues) => void;
  capacity?: number;
};

const GuestSelector = ({ guestCount, onChange, capacity }: Props) => {
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);

  return (
    <>
      <div onClick={() => !dropdownOpen && setDropdownOpen(true)}>
        <Dropdown
          className='dropdown-end dropdown-bottom w-full px-2.5 py-[14px] border rounded bg-white mb-2.5 flex items-center justify-between cursor-pointer text-gray-80'
          labelClassName='w-full items-center justify-between font-normal'
          title={
            <span className='flex items-center'>
              <UserIcon className='w-4 h-4' />
              <p className='ml-1 text-sm leading-[150%]'>
                {guestCount.adults + guestCount.children} Guests
              </p>
            </span>
          }
          contentClassName={classNames(
            'w-full mt-2 rounded border transition-all shadow text-default-font p-0',
            dropdownOpen ? '!visible' : '!invisible',
          )}
          isOpen={dropdownOpen}
          showChevron
        >
          {dropdownOpen && (
            <GuestSelectorForm
              guestCount={guestCount}
              onChange={onChange}
              onClose={() => setDropdownOpen(false)}
              capacity={capacity}
            />
          )}
        </Dropdown>
      </div>
      {dropdownOpen && (
        <div
          className='drawer-overlay absolute inset-0'
          onClick={() => setDropdownOpen(!dropdownOpen)}
        />
      )}
    </>
  );
};

export default GuestSelector;
