'use client';

import React, { useCallback, useMemo } from 'react';

import { submitNewsletterSubscription } from '@/app/services/newsletter';
import FormHelperText from '@/app/ui/form-helper-text';
import { EMAIL_PATTERN } from '@/constants/patterns';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';

import { Icon } from '../icon';

const validateEmail = (value = ''): string => {
  if (value.trim().length === 0) {
    return `Email is required.`;
  }

  if (!value.match(EMAIL_PATTERN)) {
    return 'Invalid email address';
  }

  return '';
};

const NewsletterSubscribeForm = () => {
  const [email, setEmail] = React.useState('');
  const [error, setError] = React.useState('');
  const [progressStatus, setProgressStatus] = React.useState<ProgressStatus | null>(null);

  const helperText = useMemo(
    () =>
      error
        ? error
        : progressStatus === ProgressStatus.SUCCESSFUL
        ? 'Subscription Request Submitted'
        : '',
    [error, progressStatus],
  );

  const onChange = (e: any) => {
    const value = e.target.value;
    setError(validateEmail(value));
    setEmail(value);
  };

  const onSubmit = useCallback(() => {
    if (!error && email.trim().length > 0) {
      setProgressStatus(ProgressStatus.LOADING);
      submitNewsletterSubscription({ email })
        .then((data) => {
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          window?.analytics?.track(SegmentEvents.EMAIL_SUBSCRIBED, {
            email: email,
            url: document.URL,
            referrer: document.referrer,
          });
        })
        .catch((e) => {
          console.log(e);
          setProgressStatus(ProgressStatus.FAILED);
        });
    }
  }, [email, error]);

  return (
    <>
      <div className='relative'>
        <input
          value={email}
          onChange={onChange}
          type='email'
          className='input outline-1 w-full border-white rounded lg:w-80 bg-transparent text-white mb-5 placeholder-white lg:mb-0 lg:h-12'
          placeholder='Email address'
        />
        {helperText && (
          <div className='ml-2 absolute'>
            <FormHelperText
              className={progressStatus === ProgressStatus.SUCCESSFUL ? 'text-green-500' : ''}
              error={!!error}
            >
              {helperText}
            </FormHelperText>
          </div>
        )}
      </div>
      <div
        onClick={onSubmit}
        className='w-full lg:w-80 h-14 px-10 py-4 rounded bg-white text-slate-800 text-center font-semibold mb-8 lg:mb-0 lg:h-12 lg:py-3 lg:max-w-[180px] cursor-pointer'
      >
        <span className='flex gap-x-1 justify-center'>
          {progressStatus === ProgressStatus.LOADING ? (
            'Submitting...'
          ) : (
            <>
              Subscribe <Icon icon='arrow-right' height={19} width={19} />
            </>
          )}
        </span>
      </div>
    </>
  );
};

export default NewsletterSubscribeForm;
