'use client';

import { memo, useCallback, useState } from 'react';

import { acceptLeadRequest, declineLeadRequest } from '@/app/services/lead-request';
import { revalidateTagByName } from '@/app/services/revalidateTag';
import LoadingSpinner from '@/clients/ui/loading-spinner';

type Props = {
  distribution_uuid: string;
  user: string;
};

const LeadActionButtons = ({ distribution_uuid, user }: Props) => {
  const [progressStatus, setProgressStatus] = useState<'declining' | 'accepting' | null>(null);

  const onDecline = useCallback(async () => {
    setProgressStatus('declining');
    try {
      await declineLeadRequest(distribution_uuid, user);
      await revalidateTagByName(
        `rental-opportunity-distribution/${distribution_uuid}?user=${user}`,
      );
      setTimeout(() => setProgressStatus(null), 200);
    } catch (error) {
      console.error(error);
      setProgressStatus(null);
    }
  }, [distribution_uuid, user]);

  const onAccept = useCallback(async () => {
    setProgressStatus('accepting');
    try {
      await acceptLeadRequest(distribution_uuid, user);
      await revalidateTagByName(
        `rental-opportunity-distribution/${distribution_uuid}?user=${user}`,
      );
      setTimeout(() => setProgressStatus(null), 200);
    } catch (error) {
      console.error(error);
      setProgressStatus(null);
    }
  }, [distribution_uuid, user]);

  return (
    <div className='flex items-center justify-center gap-x-8 mt-8'>
      <div className='cursor-pointer' onClick={onDecline}>
        {progressStatus === 'declining' ? (
          <div className='w-[80px] h-[80px] flex items-center justify-center bg-[#FF0000] rounded-full'>
            <LoadingSpinner className='w-8 h-8 text-white' />
          </div>
        ) : (
          <svg
            width='100'
            height='100'
            viewBox='0 0 100 100'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              opacity='0.4'
              d='M50 91.6667C73.0119 91.6667 91.6667 73.0119 91.6667 50C91.6667 26.9882 73.0119 8.33337 50 8.33337C26.9881 8.33337 8.33334 26.9882 8.33334 50C8.33334 73.0119 26.9881 91.6667 50 91.6667Z'
              fill='#FF0000'
            />
            <path
              d='M54.4167 50L64 40.4167C65.2083 39.2083 65.2083 37.2083 64 36C62.7917 34.7917 60.7917 34.7917 59.5833 36L50 45.5833L40.4167 36C39.2083 34.7917 37.2083 34.7917 36 36C34.7917 37.2083 34.7917 39.2083 36 40.4167L45.5833 50L36 59.5833C34.7917 60.7917 34.7917 62.7917 36 64C36.625 64.625 37.4167 64.9167 38.2083 64.9167C39 64.9167 39.7917 64.625 40.4167 64L50 54.4167L59.5833 64C60.2083 64.625 61 64.9167 61.7917 64.9167C62.5833 64.9167 63.375 64.625 64 64C65.2083 62.7917 65.2083 60.7917 64 59.5833L54.4167 50Z'
              fill='#FF0000'
            />
          </svg>
        )}
      </div>
      <div className='cursor-pointer' onClick={onAccept}>
        {progressStatus === 'accepting' ? (
          <div className='w-[80px] h-[80px] flex items-center justify-center bg-[#56AD2E] rounded-full'>
            <LoadingSpinner className='w-8 h-8 text-white' />
          </div>
        ) : (
          <svg
            width='100'
            height='100'
            viewBox='0 0 100 100'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              opacity='0.4'
              d='M50 91.6667C73.0119 91.6667 91.6667 73.0119 91.6667 50C91.6667 26.9882 73.0119 8.33337 50 8.33337C26.9881 8.33337 8.33334 26.9882 8.33334 50C8.33334 73.0119 26.9881 91.6667 50 91.6667Z'
              fill='#56AD2E'
            />
            <path
              d='M44.0834 64.9166C43.25 64.9166 42.4584 64.5833 41.875 63.9999L30.0834 52.2083C28.875 50.9999 28.875 48.9999 30.0834 47.7916C31.2917 46.5833 33.2917 46.5833 34.5 47.7916L44.0834 57.3749L65.5 35.9583C66.7084 34.7499 68.7084 34.7499 69.9167 35.9583C71.125 37.1666 71.125 39.1666 69.9167 40.3749L46.2917 63.9999C45.7084 64.5833 44.9167 64.9166 44.0834 64.9166Z'
              fill='#56AD2E'
            />
          </svg>
        )}
      </div>
    </div>
  );
};

export default memo(LeadActionButtons);
