'use client';

import { memo, useEffect, useMemo, useState } from 'react';

type Props = {
  initialSeconds: number;
  onComplete?: () => void;
};

const CountdownTimer = ({ initialSeconds, onComplete }: Props) => {
  const [timeLeft, setTimeLeft] = useState(initialSeconds);

  useEffect(() => {
    if (timeLeft <= 0) {
      onComplete?.();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => prevTime - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, onComplete]);

  const hours = Math.floor(timeLeft / 3600);
  const minutes = Math.floor((timeLeft % 3600) / 60);
  const seconds = timeLeft % 60;

  return (
    <span className='text-[32px] font-bold'>
      {hours > 0 && (
        <span className='text-[32px] font-bold'>{String(hours).padStart(2, '0')}:</span>
      )}
      {String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
    </span>
  );
};

export default memo(CountdownTimer);
