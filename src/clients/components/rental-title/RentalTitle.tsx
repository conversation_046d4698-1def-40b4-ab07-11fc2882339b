'use client';

import { useEffect, useState } from 'react';

import StarRating, { Rating } from '@/app/components/common/star-rating/StarRating';
import { roundInteger } from '@/utils/common';

import dynamic from 'next/dynamic';
import { useSearchParams } from 'next/navigation';

const RateModal = dynamic(() => import('./RateModal'), { ssr: false });

type RentalTitleProps = {
  title: string;
  listingId: number;
  avgRating?: number;
  ratingCount?: number;
};

export const RentalTitle = ({ title, avgRating, listingId, ratingCount }: RentalTitleProps) => {
  const [showRateModal, setShowRateModal] = useState<boolean>(false);
  const [rate, setRate] = useState<Rating | null>(null);
  const router = useSearchParams();
  const listingRatingCode = router.get('listing_rating');

  const onRate = (rate: Rating) => {
    setRate(rate);
    setShowRateModal(true);
  };

  useEffect(() => {
    if (listingRatingCode || listingRatingCode === '') {
      setRate(5);
      setShowRateModal(true);
    }
  }, [listingRatingCode]);

  return (
    <>
      <div className='flex flex-wrap justify-between py-5 lg:border-b lg:mb-5'>
        <h1 className='hidden text-[20px] font-semibold md:block'>{title}</h1>
        <div className='text-center flex flex-col'>
          <StarRating rating={5} onRate={onRate} />
          <p className='text-sm mt-2'>
            {avgRating
              ? `Average Rating: ${roundInteger(avgRating)} ( ${ratingCount} ${
                  ratingCount && ratingCount > 1 ? 'reviews' : 'review'
                })`
              : 'No verified ratings yet'}{' '}
          </p>
        </div>
      </div>
      {showRateModal && (
        <RateModal
          open={showRateModal}
          setShowRateModal={setShowRateModal}
          rate={rate}
          title={title}
          listingId={listingId}
          listingRatingCode={listingRatingCode}
        />
      )}
    </>
  );
};
