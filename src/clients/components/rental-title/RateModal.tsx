'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useCallback, useEffect, useMemo, useState } from 'react';

import { XMarkIcon } from '@heroicons/react/24/outline';

import StarRating, { Rating } from '@/app/components/common/star-rating/StarRating';
import { submitListingRating } from '@/app/services/rating';
import FormHelperText from '@/app/ui/form-helper-text';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { EMAIL_PATTERN } from '@/constants/patterns';
import { ProgressStatus } from '@/types/common';

type Props = {
  open: boolean;
  setShowRateModal: (_c: boolean) => void;
  rate: Rating | null;
  title: string;
  listingId: number;
  listingRatingCode?: string | null;
};

export type FormValues = {
  name: string;
  email: string;
  rating: Rating | null;
  comment: string;
};

const AUTOCLOSE_TIMEOUT_MS = 2000;

const RateModal = ({
  open,
  setShowRateModal,
  title,
  rate,
  listingId,
  listingRatingCode,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const initialState = useMemo(() => {
    if (!listingRatingCode) {
      return {
        name: '',
        email: '',
        rating: rate,
        comment: '',
      };
    }
    const ratingData = JSON.parse(
      Buffer.from(listingRatingCode as string, 'base64').toString('ascii'),
    );
    return {
      name: ratingData?.name ?? '',
      email: ratingData?.email ?? '',
      rating: rate,
      comment: '',
    };
  }, [listingRatingCode, rate]);

  const { formState, onResetForm, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    initialState,
    {
      name: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Name is required.`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Email is required.`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      rating: (_v, _n, _value) => {
        if (!_value) {
          return 'Rating is required';
        }
      },
    },
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );

  const [message, setMessage] = useState('');

  const onFormSubmit: FormEventHandler<HTMLFormElement> = (e: React.SyntheticEvent) => {
    e.preventDefault();

    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    submitListingRating(listingId, {
      name: formState.name,
      email: formState.email,
      rating: formState.rating,
      comment: formState.comment,
    })
      .then((res) => {
        if (res) {
          setMessage('Rate successfully submitted!');
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          onResetForm();
          setTimeout(() => {
            setShowRateModal(false);
            setMessage('');
          }, AUTOCLOSE_TIMEOUT_MS);
        }
      })
      .catch(() => {
        setMessage('Something went wrong. Please try again later');
        setProgressStatus(ProgressStatus.FAILED);
        setTimeout(() => {
          setMessage('');
        }, AUTOCLOSE_TIMEOUT_MS);
      });
  };

  return (
    <Modal
      open={open}
      onClose={() => setShowRateModal(false)}
      className='min-h-[100vh] md:min-h-full'
    >
      <div className=' relative'>
        <XMarkIcon
          onClick={() => setShowRateModal(false)}
          className='w-5 h-5 absolute top-4 right-4 cursor-pointer text-gray-main'
        />
        <div className='px-6 py-[50px] sm:py-10'>
          <div className='flex justify-between'>
            <h3 className='text-2xl font-semibold text-cyan-900'>Rate : {title}</h3>
          </div>
          <hr />
          <div className='py-4 grid grid-cols-1 gap-y-2'>
            <p className='text-base my-1'>Fill details below to submit.</p>
            <form className='grid grid-cols-1 gap-y-6' onSubmit={onFormSubmit}>
              <div className='relative mb-4'>
                <StarRating
                  rating={formState.rating as Rating}
                  onRate={(_rate) => onChange(_rate, 'rating')}
                />
                {!!errors.rating && (
                  <div className='ml-2 absolute'>
                    <FormHelperText error>{errors.rating}</FormHelperText>
                  </div>
                )}
              </div>

              <Input
                type='text'
                name='name'
                className='w-full'
                value={formState.name}
                onChange={onChangeTextInput}
                required
                helperText={errors.name ?? ''}
                error={!!errors.name}
                placeholder='Name'
              />
              <Input
                type='email'
                name='email'
                className='w-full'
                value={formState.email}
                onChange={onChangeTextInput}
                helperText={errors.email ?? ''}
                error={!!errors.email}
                required
                placeholder='Email'
              />
              <Textarea
                name='comment'
                className='w-full p-2'
                type='textarea'
                placeholder='Help a future guest by letting them know what you thought of the house'
                helperText={errors?.comment ?? ''}
                error={!!errors?.comment?.length}
                onChange={onChangeTextInput}
              />
              <Button
                disabled={progressStatus === ProgressStatus.LOADING}
                isSubmit
                className='mt-1'
                title={
                  progressStatus === ProgressStatus.LOADING ? 'Submitting...' : 'Submit Your Rating'
                }
              />
            </form>
            {message && <p className='text-cyan-900 font-normal text-sm'>{message}</p>}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default RateModal;
