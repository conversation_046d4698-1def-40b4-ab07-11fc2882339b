import React, { memo } from 'react';

import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

import dayjs from 'dayjs';

type Props = {
  monthDate: Date;
  date: Date;
  changeYear(year: number): void;
  changeMonth(month: number): void;
  customHeaderCount: number;
  decreaseMonth(): void;
  increaseMonth(): void;
  prevMonthButtonDisabled: boolean;
  nextMonthButtonDisabled: boolean;
  decreaseYear(): void;
  increaseYear(): void;
  prevYearButtonDisabled: boolean;
  nextYearButtonDisabled: boolean;
};

const CustomHeader = ({
  monthDate,
  decreaseMonth,
  prevMonthButtonDisabled,
  increaseMonth,
  nextMonthButtonDisabled,
  ...rest
}: Props) => {
  return (
    <div className='flex items-center justify-between'>
      <button
        onClick={(e) => {
          e.preventDefault();
          decreaseMonth();
        }}
        disabled={prevMonthButtonDisabled}
        className={`${prevMonthButtonDisabled ? 'text-disabled' : ''}`}
      >
        <ChevronLeftIcon className='w-4 h-4' />
      </button>
      <p>{dayjs(monthDate).format('MMMM YYYY')} </p>
      <button
        onClick={(e) => {
          e.preventDefault();
          increaseMonth();
        }}
        disabled={nextMonthButtonDisabled}
        className={`${nextMonthButtonDisabled ? 'text-disabled' : ''}`}
      >
        <ChevronRightIcon className='w-4 h-4' />
      </button>
    </div>
  );
};

export default memo(CustomHeader);
