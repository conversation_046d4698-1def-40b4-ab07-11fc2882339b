'use client';

import React from 'react';

import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { DateRange } from '.';
import CustomHeader from './CustomHeader';
import './style.css';

type Props = {
  value?: DateRange;
  onChange: (date: DateRange, event: React.SyntheticEvent) => void;
  filterDate?: (date: Date) => boolean;
  isMobile?: boolean;
  minDate?: Date;
  maxDate?: Date;
};

const DateRangePickerComponent = ({
  value = [null, null],
  onChange,
  isMobile,
  filterDate,
  minDate = new Date(),
  maxDate,
}: Props) => {
  return (
    <div className='absolute top-[55px] right-[50%] translate-x-[50%] md:right-0 md:translate-x-0 z-[99999]'>
      <DatePicker
        onChange={onChange}
        startDate={value[0]}
        endDate={value[1]}
        selectsRange
        filterDate={filterDate}
        monthsShown={isMobile ? 1 : 2}
        calendarClassName='!flex !font-poppins'
        formatWeekDay={(nameOfDay) => nameOfDay.slice(0, 1)}
        renderCustomHeader={({ ...props }) => <CustomHeader {...props} />}
        dayClassName={() =>
          '!w-[40px] !h-[40px] !inline-flex !items-center !justify-center !rounded-[50%] !m-[1px]'
        }
        minDate={minDate}
        maxDate={maxDate}
        inline
      />
    </div>
  );
};

export default DateRangePickerComponent;
