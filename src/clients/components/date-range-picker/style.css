.react-datepicker__header {
  background: white;
  padding: 16px 16px 8px;
  border-bottom: 1px solid #e1e9ef;
}

.react-datepicker__month-container:not(:last-child) {
  border-right: 1px solid #e1e9ef;
}

.react-datepicker__month-container {
  padding-bottom: 8px;
}

.react-datepicker__day-name {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 1px;
}

.react-datepicker__week {
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-datepicker__day--outside-month,
.react-datepicker__day--outside-month:hover {
  visibility: hidden;
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__day--range-start,
.react-datepicker__day--in-range {
  background-color: #1c4a5e;
  color: white;
}

.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__day--range-start:hover,
.react-datepicker__day--in-range:hover {
  background-color: #4c737f;
}

.react-datepicker__day--keyboard-selected.react-datepicker__day--disabled {
  background-color: inherit;
  color: #ccc;
}
