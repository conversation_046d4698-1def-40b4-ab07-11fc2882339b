'use client';

import { useCallback, useMemo, useState } from 'react';

import { CalendarDaysIcon } from '@heroicons/react/24/outline';

import FormHelperText from '@/app/ui/form-helper-text';

import classNames from 'classnames';
import dayjs from 'dayjs';
import dynamic from 'next/dynamic';
import { twMerge } from 'tailwind-merge';

export const formatDate = (date: Date, format = 'MMM D,yyyy'): string => dayjs(date).format(format);

const DateRangePickerComponent = dynamic(() => import('./DateRangePickerComponent'), {
  ssr: false,
});

export type DateRange = (Date | null | undefined)[];

type Props = {
  value?: DateRange;
  isMobile?: boolean;
  className?: string;
  helperText?: string;
  onChange: (value: any, name: string) => void;
  preventOnChange?: (range: any) => boolean;
  filterDate?: (date: Date) => boolean;
  error?: boolean;
  name?: string;
  minDate?: Date;
  maxDate?: Date;
};

const DateRangePicker = ({
  value: range = [null, null],
  isMobile,
  className = '',
  onChange,
  helperText = '',
  filterDate,
  error,
  name,
  minDate,
  maxDate,
  preventOnChange,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const onChangeDates = useCallback(
    (dates: DateRange, event: React.SyntheticEvent) => {
      event.stopPropagation();
      if (preventOnChange?.(dates)) {
        onChange([null, null], name ?? '');
        return;
      }
      onChange(dates, name ?? '');
      if (dates?.every((_v) => dayjs(_v).isValid())) {
        setOpen(false);
      }
    },
    [name, onChange, preventOnChange],
  );

  const formattedValue = useMemo(
    () =>
      !range[0] || !range[1]
        ? 'Check in - Check out'
        : `${formatDate(range[0], 'MMM D')} - ${formatDate(range[1], 'MMM D, YYYY')}`,
    [range],
  );

  return (
    <>
      <div
        className={twMerge(
          classNames(
            'relative px-2.5 py-[14px] border rounded bg-white mb-2.5 flex items-center cursor-text',
            (!range[0] || !range[1]) && 'text-gray-80',
            error && 'border-red-main',
          ),
          className,
        )}
        onClick={() => setOpen(true)}
      >
        <CalendarDaysIcon className='w-4 h-4' />
        <p className='ml-1 text-sm leading-[150%]'>{formattedValue}</p>
        {open && (
          <DateRangePickerComponent
            value={range}
            onChange={onChangeDates}
            filterDate={filterDate}
            isMobile={isMobile}
            minDate={minDate}
            maxDate={maxDate}
          />
        )}
        {helperText && (
          <div className='ml-2 absolute top-[38px]'>
            <FormHelperText error={error ? true : undefined}>{helperText}</FormHelperText>
          </div>
        )}
      </div>
      {open && (
        <div className='drawer-overlay fixed inset-0 z-[9999]' onClick={() => setOpen(!open)} />
      )}
    </>
  );
};

export default DateRangePicker;
