import { useEffect } from 'react';
import { ReadonlyURLSearchParams } from 'next/navigation';

/**
 * Custom hook for tracking page views with analytics
 * Separates analytics tracking logic from the main component
 */
export const useAnalyticsPageTracking = (
  pathname: string,
  searchParams: ReadonlyURLSearchParams
) => {
  useEffect(() => {
    // Ensure analytics is available before tracking
    if (typeof window !== 'undefined' && window.analytics) {
      window.analytics.page({
        path: pathname,
        url: document.URL,
        search: searchParams.toString(),
        referrer: document.referrer,
      });
    }
  }, [pathname, searchParams]);
};
