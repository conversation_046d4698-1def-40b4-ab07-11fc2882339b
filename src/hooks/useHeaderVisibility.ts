import { useMemo } from 'react';

/**
 * Custom hook for determining header visibility based on pathname
 * Makes the header visibility logic reusable and testable
 */
export const useHeaderVisibility = (pathname: string) => {
  const hideHeader = useMemo(() => {
    // Add more conditions here as needed
    const hiddenPaths = ['lead-request'];
    return hiddenPaths.some(path => pathname.includes(path));
  }, [pathname]);

  return { hideHeader };
};
