import { useEffect } from 'react';

import { MOBILE_MENU_DRAWER_ID } from '@/constants/ui';

/**
 * Custom hook for automatically closing mobile menu on route changes
 * Separates mobile menu logic from the main component
 */
export const useMobileMenuAutoClose = (pathname: string) => {
  useEffect(() => {
    // Close mobile menu if it's open when pathname changes
    const mobileMenuDrawer = document?.getElementById(MOBILE_MENU_DRAWER_ID) as HTMLInputElement;

    if (mobileMenuDrawer?.checked) {
      mobileMenuDrawer.click();
    }
  }, [pathname]);
};
