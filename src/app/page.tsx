import { getSearchParams } from '@/app/utils';

import { Metadata } from 'next';

import { BlogPreview } from '../clients/components/blog-preview/BlogPreview';
import { RealEstatePreview } from '../clients/components/real-estate-preview/RealEstatePreview';

import { Banner } from './components/home/<USER>';
import { BlogCard } from './components/home/<USER>';
import { Newsletter } from './components/home/<USER>';
import { RentalsPreview } from './components/home/<USER>';
import { PropertyCard as PropertyRentalCard } from './components/rental-listing/PropertyCard';

export const revalidate = 600;

const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

type MetaDataProps = {
  title_tag: string;
  meta_description: string;
  meta_robots: string;
  meta_canonical: string;
};
export async function generateMetadata(): Promise<Metadata> {
  const seo = (await fetch(`${baseUrl}/seo-metadata/homepage`).then((res) =>
    res.json(),
  )) as MetaDataProps;
  return {
    title: seo.title_tag,
    description: seo.meta_description,
    alternates: {
      canonical: seo.meta_canonical,
    },
  };
}
async function getData() {
  const A = {
    limit: 8,
    offset: 0,
    new_listings: '7',
    dordering: '-ModificationTimestamp',
    status: 'A',
  };
  const activeData = await fetch(`${baseUrl}/link-listings-v2?` + getSearchParams(A));
  // Under Agreement
  const U = {
    limit: 8,
    offset: 0,
    dordering: '-ModificationTimestamp',
    status: 'U',
  };
  const underAgreementData = await fetch(`${baseUrl}/link-listings-v2?` + getSearchParams(U));
  // Recent Sales
  const S = {
    limit: 8,
    offset: 0,
    close_date: 7,
    dordering: '-ModificationTimestamp',
    status: 'S',
  };
  const recentSalesData = await fetch(`${baseUrl}/link-listings-v2?` + getSearchParams(S));
  //  Price Adjustments
  const P = {
    limit: 8,
    offset: 0,
    price_change: 7,
    dordering: '-ModificationTimestamp',
    status: 'A',
  };
  const priceAdjustmentsData = await fetch(`${baseUrl}/link-listings-v2?` + getSearchParams(P));

  const rentals = await fetch(
    `${baseUrl}/listings?show_ratings=1&limit=5&show_price=true&ordering=priority%2C-calendar_updated_at`,
  );
  const posts = await fetch(
    'https://wordpress.congdonandcoleman.com/wp-json/wp/v2/posts?per_page=6',
  );

  return {
    new_listing: await activeData.json(),
    under_agreement: await underAgreementData.json(),
    recent_sales: await recentSalesData.json(),
    price_change: await priceAdjustmentsData.json(),
    posts: await posts.json(),
    rentals: (await rentals.json()).results,
  };
}

function readableFormatDate(dateStr: string) {
  const date = new Date(dateStr);
  // Format the date to "Month DD, YYYY"
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
  }).format(date);
}

export default async function Home() {
  const { new_listing, under_agreement, recent_sales, price_change, posts, rentals } =
    await getData();

  const blogList = posts.map((post: any) => {
    const date = post.modified || post.date ? readableFormatDate(post.modified ?? post.date) : '';
    const article = {
      image: post.jetpack_featured_media_url,
      date,
      author: 'C & C',
      title: post.title.rendered.replace(/(<([^>]+)>)/gi, ''),
      text: post.excerpt.rendered.replace(/(<([^>]+)>)/gi, ''),
      slug: '/blog/' + post.slug,
    };
    return (
      <div className='md:pr-7 lg:pb-4 lg:pr-0 lg:flex lg:justify-items-center' key={article.slug}>
        <BlogCard article={article} className='shadow-md w-full lg:mx-auto' />
      </div>
    );
  });

  const realEstateMap = {
    // exclusive: exclusive_listing,
    new: new_listing,
    ua: under_agreement,
    recent: recent_sales,
    price: price_change,
  };

  return (
    <>
      <Banner />
      <RealEstatePreview listMap={realEstateMap} />
      <RentalsPreview>
        {rentals.map((property: any) => {
          return (
            <div className='pr-4 xl:pr-7' key={property.listing_id}>
              <PropertyRentalCard
                property={property}
                className='shadow-md w-[284px] xl:[320px] bg-white'
              />
            </div>
          );
        })}
      </RentalsPreview>
      <BlogPreview>
        <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4'>{blogList}</div>
      </BlogPreview>
      <Newsletter />
    </>
  );
}
