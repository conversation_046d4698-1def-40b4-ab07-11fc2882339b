import { Pagination } from '@/app/components/common/Pagination';
import { TopBanner } from '@/app/components/common/TopBanner';
import { PropertyCard } from '@/app/components/real-estate/PropertyCard';
import RealEstateTopFiltersWrapper from '@/app/components/real-estate/RealEstateTopFiltersWrapper';
import { getSearchParams } from '@/app/utils';
import ClientWrapper from '@/clients/components/nantucket-real-estate/ClientWrapper';
import RentalTopFilters from '@/clients/components/rentals-top-filters/RentalTopFilters';
import { TopFilterType } from '@/types/common';

import { Metadata } from 'next';

export const revalidate = 600;

type MetaDataProps = {
  title_tag: string;
  meta_description: string;
  meta_robots: string;
  meta_canonical: string;
};

const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

export async function generateMetadata(): Promise<Metadata> {
  const seo = (await fetch(`${baseUrl}/seo-metadata/buy_listing`).then((res) =>
    res.json(),
  )) as MetaDataProps;
  return {
    title: seo.title_tag,
    description: seo.meta_description,
    alternates: {
      canonical: seo.meta_canonical,
    },
  };
}

const perPage = 32;

async function getData(searchParams: any) {
  const sp = Object.assign(
    {
      limit: perPage,
      offset: 0,
      show_price: true,
      status: 'A',
      ordering: '-ListPrice',
    },
    searchParams,
  );
  const params = getSearchParams(sp);
  const list = await fetch(`${baseUrl}/link-listings-v2?` + params);
  return {
    list: await list.json(),
  };
}

export default async function RentalListing({
  searchParams,
}: {
  searchParams: { offset: number };
}) {
  const { list } = await getData(searchParams);
  const pagesCount = Math.ceil(list.count / perPage);

  const currentPage = Math.ceil(searchParams.offset ?? 0 / perPage) + 1;
  const totalPage = Math.min(Math.min(list.count, perPage) + currentPage - 1, list.count);
  const filterTitle =
    list.count > 0 ? `Showing ${currentPage}-${totalPage} of ${list.count} listings` : '';

  return (
    <div>
      <ClientWrapper realEstates={list.results} searchParams={searchParams}>
        <TopBanner title='Real estate & homes for sale in Nantucket, MA' breadcrumb='Buy' />
        <div className='xl:container'>
          <div className='px-4 pb-14 pt-10'>
            <RealEstateTopFiltersWrapper>
              <RentalTopFilters
                title={filterTitle}
                propertyCount={list?.count}
                filterType={TopFilterType.REAL_ESTATE}
              />
            </RealEstateTopFiltersWrapper>
            <div className='gap-x-3 gap-5 pb-10 grid grid-cols-1 md:gap-x-5 md:gap-y-7.5 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {list?.results?.map((property: any, index: number) => (
                <PropertyCard key={property.link_id} property={property} preload={index === 0} />
              ))}
            </div>
            {pagesCount > 1 && (
              <div className='flex justify-center lg:justify-end'>
                <Pagination
                  queryParams={searchParams}
                  pageCount={pagesCount}
                  prefix='/nantucket-real-estate'
                />
              </div>
            )}
          </div>
        </div>
      </ClientWrapper>
    </div>
  );
}
