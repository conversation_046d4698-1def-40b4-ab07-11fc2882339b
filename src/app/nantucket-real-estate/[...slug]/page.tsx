import { Newsletter } from '@/app/components/home/<USER>';
import MediaView from '@/app/components/rental-listing-details/MediaView';
import { MlsStatus } from '@/app/types';
import RealEstateWrapper from '@/clients/components/nantucket-real-estate/RealEstateWrapper';
import ContactAgentForm from '@/clients/components/real-estate-detail-form';
import MediaViewWrapper from '@/clients/components/rental-listing-details/MediaViewWrapper';
import ShareMenu from '@/clients/components/share-menu';
import Button from '@/clients/ui/button';
import { IListingImage } from '@/types/rental-listing-details';
import { formatCurrency } from '@/utils/common';
import { isMobileDevice } from '@/utils/responsive';

import dayjs from 'dayjs';
import { Metadata, ResolvingMetadata } from 'next';
import { headers } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

export const revalidate = 600;

const MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';
const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';
type PageProps = {
  params: { slug: string | string[] };
};

const getRealEstateBySlug = async (slug: string) => {
  try {
    const res = await fetch(`${BASE_URL}/link-listings-v2?slug=${slug}`, {
      next: { revalidate: 600, tags: [slug] },
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch data: ${res.status}`);
    }

    return res.json();
  } catch (error) {
    console.error(`Error fetching real estate by slug ${slug}:`, error);
    throw error; // Re-throw to be handled by the caller
  }
};

export async function generateMetadata(
  { params }: PageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const formattedSlug = (params.slug as string[]).join('/') ?? '';

  try {
    const response = await getRealEstateBySlug(formattedSlug);
    const listing = (response as any).results?.[0];

    // If no listing found, return default metadata
    if (!listing) {
      return {
        title: 'Property Not Found | Congdon & Coleman',
        description: 'The requested property could not be found.',
        metadataBase: new URL('https://www.congdonandcoleman.com'),
      };
    }

    const previousImages = (await parent)?.openGraph?.images || [];

    return {
      title: listing?.TitleTag ?? '',
      description: listing?.MetaDescription ?? '',
      metadataBase: new URL('https://www.congdonandcoleman.com'),
      alternates: {
        canonical: listing?.MetaCanonical ?? '',
      },
      robots: listing?.MetaRobots ?? '',
      openGraph: {
        images: [...previousImages],
      },
    };
  } catch (error) {
    // In case of error, return default metadata
    return {
      title: 'Property Not Found | Congdon & Coleman',
      description: 'The requested property could not be found.',
      metadataBase: new URL('https://www.congdonandcoleman.com'),
    };
  }
}

const getNeighborhoods = async (params: string) => {
  const res = await fetch(`${BASE_URL}/area?${params}`, {
    next: { revalidate: 600, tags: [params] },
  });

  if (!res.ok) {
    throw new Error('Failed fetching neighborhood information');
  }

  return res.json();
};

const prefix = process.env.VERCEL_ENV === 'production' ? 'https://www' : 'https://dev';

const mlsStatusToLabelMap: Record<MlsStatus, { label: string; bg: string }> = {
  A: { label: 'Available', bg: 'bg-[#71B283]' },
  C: { label: 'Under Contract, Sill Showing', bg: 'bg-[#F1B51A]' },
  OP: { label: 'Under Contract', bg: 'bg-[#F1B51A]' },
  U: { label: 'Under Contract', bg: 'bg-[#F1B51A]' },
  S: { label: 'Sold', bg: 'bg-[#ED5950]' },
  X: { label: 'Removed from Market', bg: 'bg-[#BE7BF3]' },
  PS: { label: 'Purchase & Sales', bg: 'bg-[#BE7BF3]' },
};

export type IRealEstateDetail = {
  MLSAreaMajor: string;
  MlsStatus: MlsStatus;
  CloseDate?: string;
  ClosePrice?: number;
  link_images: IListingImage[];
  StreetNumber: string;
  StreetName: string;
  ListPrice: number;
  PropertyType: string;
  Slug: string;
  RoomsTotal: number;
  BedroomsTotal: number;
  BathroomsTotalDecimal: number;
  LotSizeSquareFeet: number;
  BuildingAreaTotal: number;
  PublicRemarks: string;
  ListAgentFullName: string;
  YearBuilt: string;
  YearBuiltEffective: string;
  Address: string;
  link_id: number;
  View: string[];
  Zoning: string | null;
  TaxAssessedValue: number;
  TaxAnnualAmount: number;
  ListOfficeName: string;
};

type SaleInformationProps = {
  closeDate: string;
  closePrice: number;
  listPrice: number;
};

const SaleInformation = ({ closeDate, closePrice, listPrice }: SaleInformationProps) => (
  <div>
    <div className='flex flex-col text-sm'>
      <div className='grid grid-cols-2'>
        <div>
          <span className='text-normal font-semibold'>Sale Date:</span>
        </div>
        <div>{dayjs(closeDate).format('MMMM DD, YYYY')}</div>
      </div>
      {closePrice ? (
        <div className='grid grid-cols-2'>
          <div>
            <span className='text-normal font-semibold'>Sale Price:</span>
          </div>
          <div>${new Intl.NumberFormat('en-US').format(closePrice)}</div>
        </div>
      ) : null}
      <div className='grid grid-cols-2'>
        <div>
          <span className='text-normal font-semibold'>Last Ask:</span>
        </div>
        <div>${new Intl.NumberFormat('en-US').format(listPrice)}</div>
      </div>
    </div>
  </div>
);

export default async function RentalListingDetails({ params }: PageProps) {
  const userAgent = headers().get('user-agent');
  const isMobile = isMobileDevice(userAgent ?? '');
  const formattedSlug = (params.slug as string[]).join('/') ?? '';

  try {
    const response = await getRealEstateBySlug(formattedSlug);
    const detail = (response as { results: IRealEstateDetail[] }).results?.[0];

    // If no details found, return 404
    if (!detail) {
      return notFound();
    }

    const neighbourhoods = ((await getNeighborhoods('limit=50')) as any).results;
    const neighbourhood = neighbourhoods.find(
      (neighborhood: any) => neighborhood.name === detail.MLSAreaMajor,
    );

    const propertyAddress = `${detail.StreetNumber} ${detail.StreetName} `;
    const statusLabel =
      detail.MlsStatus && detail.MlsStatus in mlsStatusToLabelMap
        ? mlsStatusToLabelMap[detail.MlsStatus]
        : mlsStatusToLabelMap.A;

    const shouldShowSaleInfoBox = detail?.CloseDate && detail.ClosePrice;

    return (
      <>
        <main className='px-4 xl:container pt-4 md:pt-0 pb-2.5 md:mb-[60px] md:mt-4 md:rounded'>
          <RealEstateWrapper realEstateDetails={detail}>
            <div className='flex flex-nowrap justify-between pb-5'>
              <div>
                <h3 className='text-lg text-center text-navy-blue'>{propertyAddress}</h3>
              </div>
              <div>
                <span className={`${statusLabel.bg} text-white text-xs py-1.5 px-2.5 rounded`}>
                  {statusLabel.label}
                </span>
              </div>
            </div>
            <div className='xl:flex items-start xl:gap-x-2.5 xl:justify-between'>
              <MediaViewWrapper images={detail.link_images} isMobile={isMobile}>
                <MediaView
                  className='md:w-full'
                  images={detail.link_images}
                  isMobile={isMobile}
                  propertyHeadline={`${detail.StreetNumber} ${detail.StreetName}  | ${detail.MLSAreaMajor}`}
                />
              </MediaViewWrapper>
              <div className='w-full xl:w-[350px]'>
                <div className='border rounded bg-[#F5F9FF] mt-7 mb-3 md:my-5 xl:my-2.5 py-4 xl:pt-0 flex flex-col items-center xl:mt-0 xl:items-start xl:px-2.5'>
                  <h1 className='order-2 text-3xl text-navy-blue text-center font-semibold md:text-left'>
                    ${new Intl.NumberFormat('en-US').format(detail.ListPrice)}
                  </h1>
                  <h4 className='order-3 py-1 text-lg text-black-3 text-center md:text-left'>
                    {detail.PropertyType == '1 Family' ? 'Single Family' : detail.PropertyType}
                  </h4>
                  <h2 className='order-4 text-2xl md:text-2xl text-center pb-5 md:text-left'>
                    {detail.StreetNumber} {detail.StreetName}
                  </h2>

                  <Button className='order-5 w-10/12 md:w-[330px]'>
                    <a href='#contact'>Contact Agent</a>
                  </Button>

                  <div className='mt-4 order-6 xl:order-1 xl:self-end'>
                    <ShareMenu
                      title='Share this property'
                      shareURL={`${prefix}.congdonandcoleman.com/nantucket-real-estate/${detail.Slug}`}
                      shareText='Look at this property I found on Congdon & Coleman!'
                    />
                  </div>
                  {detail.CloseDate && detail.ClosePrice && (
                    <div className='mb-4 lg:order-5 lg:mt-4 lg:mb-0'>
                      <SaleInformation
                        closeDate={detail.CloseDate}
                        closePrice={detail.ClosePrice}
                        listPrice={detail.ListPrice}
                      />
                    </div>
                  )}
                </div>
                <div className='border rounded py-5 xl:py-4 px-4 xl:px-2.5 border-gray-300 bg-[#F7FAFB] text-sm'>
                  <div className='font-semibold text-lg'>About this Property</div>
                  <hr className='mt-2.5 mb-4' />
                  <div className='grid gap-y-4 xl:gap-x-2 xl:grid-cols-3'>
                    <div className='flex justify-between xl:pb-0 xl:block'>
                      <div className='text-gray-main'>Total Rooms</div>
                      <div className='font-semibold'>{detail.RoomsTotal ?? '-'}</div>
                    </div>
                    <div className='flex justify-between xl:pb-0 xl:block'>
                      <div className='text-gray-main'>Bedrooms</div>
                      <div className='font-semibold'>{detail.BedroomsTotal ?? '-'}</div>
                    </div>
                    <div className='flex justify-between xl:pb-0 xl:block'>
                      <div className='text-gray-main'>Bathrooms</div>
                      <div className='font-semibold'>{detail.BathroomsTotalDecimal ?? '-'}</div>
                    </div>
                  </div>
                  <div className='grid gap-y-4 mt-4 xl:gap-x-2 xl:grid-cols-3'>
                    <div className='flex justify-between xl:pb-0 xl:block'>
                      <div className='text-gray-main'>Lot Size</div>
                      <div className='font-semibold'>
                        {detail.LotSizeSquareFeet
                          ? `${new Intl.NumberFormat('en-US').format(detail.LotSizeSquareFeet)} SF`
                          : '-'}
                      </div>
                    </div>
                    <div className='flex justify-between xl:pb-0 xl:block'>
                      <div className='text-gray-main'>Living Area</div>
                      <div className='font-semibold'>
                        {detail.BuildingAreaTotal
                          ? `${new Intl.NumberFormat('en-US').format(detail.BuildingAreaTotal)} SF`
                          : '-'}
                      </div>
                    </div>
                    <div className='flex justify-between xl:pb-0 xl:block'>
                      <div className='text-gray-main'>Year Built</div>
                      <div className='font-semibold'>
                        {detail.YearBuilt ?? '-'}{' '}
                        {detail.YearBuiltEffective ? '(' + detail.YearBuiltEffective + ')' : ''}
                      </div>
                    </div>
                  </div>
                  <div className='grid gap-y-4 mt-4 xl:gap-x-2 xl:grid-cols-3'>
                    <div className='flex justify-between xl:block'>
                      <div className='text-gray-main'>Views</div>
                      <div className='font-semibold'>{detail.View.join(', ')}</div>
                    </div>
                    <div className='flex justify-between xl:block'>
                      <div className='text-gray-700'>Zoning</div>
                      <div className='font-semibold'>{detail?.Zoning ?? 'None'}</div>
                    </div>
                    <div className='flex justify-between xl:block'>
                      <div className='text-gray-main'>Tax Assessment</div>
                      <div className='font-semibold'>
                        {detail.TaxAssessedValue ? formatCurrency(detail.TaxAssessedValue) : '-'}
                      </div>
                    </div>
                    <div className='flex justify-between xl:block'>
                      <div className='text-gray-main'>Estimated Taxes</div>
                      <div className='font-semibold'>
                        {detail.TaxAnnualAmount ? formatCurrency(detail.TaxAnnualAmount) : '-'}
                      </div>
                    </div>
                  </div>
                </div>
                <div className='mt-2 text-sm leading-[130%] text-gray-main px-4 xl:px-2.5'>
                  Listed by {detail.ListOfficeName ?? '-'}
                </div>
              </div>
            </div>
            <div id='contact'>
              <div className='max-w-[1092px] mx-auto mt-8 mb-10'>
                <h1 className='text-2xl mb-3 md:text-4xl font-semibold md:pt-6 md:text-center'>
                  Property Description
                </h1>
                <div className='text-gray-main leading-5 text-justify md:pb-12'>
                  {detail.PublicRemarks}
                </div>
              </div>
              <div className='max-w-[1092px] mx-auto py-8 rounded border border-gray-300 bg-[#F7FAFB] px-2 md:border-0 md:bg-white md:text-center md:flex flex-col items-center'>
                <h1 className='text-2xl md:text-4xl text-center font-semibold'>Contact An Agent</h1>
                <hr className='w-full md:block my-5 md:w-[730px] md:mx-auto' />
                <ContactAgentForm
                  neighborhood={detail.MLSAreaMajor}
                  property_address={detail.StreetNumber + ' ' + detail.StreetName}
                  agent={detail.ListAgentFullName}
                />
              </div>
              <Image
                alt='Map placeholder'
                width={0}
                height={0}
                className='w-full h-[300px] my-8 md:w-[1092px] mx-auto rounded'
                src={`https://maps.googleapis.com/maps/api/staticmap?center=${detail.Address}&zoom=16&size=1092x300&markers=color:red%7C${detail.Address}&key=${MAPS_API_KEY}`}
              />

              {neighbourhood ? (
                <div className='max-w-[1092px] mx-auto'>
                  <h1 className='text-2xl mb-10 md:text-center font-semibold'>
                    More About The Neighborhood
                  </h1>
                  <div className='relative h-[300px] w-full mb-4'>
                    <Image
                      alt='Map placeholder'
                      width={1092}
                      height={300}
                      className='w-full h-[300px] md:w-[1092px] md:h-[300px] absolute rounded'
                      src={
                        'https://d1b75btxwpe6g0.cloudfront.net/images/24ff99d1-2c1e-4342-865f-1ad18cac4af9.jpg'
                      }
                      placeholder='blur'
                      blurDataURL='https://via.placeholder.com/150'
                    />
                    <div className='absolute bottom-4 w-full text-center text-white py-2'>
                      <div className='text-lg font-semibold pb-2'>{neighbourhood.name}</div>
                      <div className='md:flex justify-center'>
                        <div className='hover:underline pb-2 md:pr-8'>
                          <Link
                            target='blank'
                            href={`/nantucket-real-estate/?area=${neighbourhood.name}`}
                          >
                            Real Estate Listings
                          </Link>
                        </div>
                        <div className='hover:underline'>
                          <Link
                            target='blank'
                            href={`/nantucket-rentals/?area=${neighbourhood.id}`}
                          >
                            Vacation Rentals
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </RealEstateWrapper>
        </main>
        <Newsletter />
      </>
    );
  } catch (error) {
    console.error('Error fetching real estate details:', error);
    return notFound();
  }
}
