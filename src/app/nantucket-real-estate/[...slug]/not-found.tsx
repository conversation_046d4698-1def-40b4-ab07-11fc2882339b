import { Newsletter } from '@/app/components/home/<USER>';
import Button from '@/clients/ui/button';

import Link from 'next/link';

export default function NotFound() {
  return (
    <div className='container mx-auto px-4 py-16 text-center'>
      <h1 className='text-4xl font-bold text-navy-blue mb-4'>Property Not Found</h1>
      <p className='text-lg mb-8'>
        We couldn&apos;t find the property you&apos;re looking for. It may have been removed or the
        URL might be incorrect.
      </p>
      <div className='flex flex-col items-center gap-4 md:flex-row md:justify-center'>
        <Button>
          <Link href='/nantucket-real-estate/'>Browse All Properties</Link>
        </Button>
        <Button intent='outline'>
          <Link href='/'>Return Home</Link>
        </Button>
      </div>
      <Newsletter />
    </div>
  );
}
