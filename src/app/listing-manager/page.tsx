import CalendlyWidget from '@/clients/components/common/CalendlyWidget';
import HoverPlayVideo from '@/clients/components/common/HoverPlayVideo';
import LoginForm from '@/clients/components/list-your-home/LoginForm';

import Image from 'next/image';
import Link from 'next/link';

import ListYourHomeHeader from '../components/list-your-home/ListYourHomeHeader';
import { H1, H2 } from '../ui/Typography';

export default function ListingManager() {
  return (
    <>
      <ListYourHomeHeader>
        <div className='flex items-center justify-center absolute inset-0 top-[44%] lg:top-[20%] left-1/2 -translate-x-1/2 -translate-y-[50%]'>
          <Link
            href='#login-form'
            scroll={true}
            className='text-center text-lg md:text-[19px] rounded-[48px] border border-solid border-white px-[48px] btn bg-primary-slate hover:bg-[#7B9EAA] text-white'
          >
            Access My Listing
          </Link>
        </div>
      </ListYourHomeHeader>
      <main>
        <div className='container py-[60px] md:py-[80px] lg:py-[100px]'>
          <H1 className='text-6 md:text-8 lg:text-[40px] font-medium text-center m-0 lg:mx-[120px] text-carolina-blue'>
            Smarter Rental <em>Management</em>
          </H1>
          <iframe
            className='w-full h-[220px] md:h-[520px] rounded-lg my-10 md:hidden'
            src='https://www.youtube.com/embed/Hgw7Bd3pXGE?si=O2eFVZjaEbPOpTc5'
            title='YouTube video player'
            frameBorder='0'
            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'
            referrerPolicy='strict-origin-when-cross-origin'
            allowFullScreen
          />
          <ul className='list-disc px-4 md:mx-auto text-grey-main mb-8 md:w-[55vw] lg:w-[40vw] 2xl:w-[35vw]'>
            <li>Tired of updating availability everywhere?</li>
            <li>Sync your listing with multiple brokerages automatically</li>
            <li>See how it works - watch the demo!</li>
          </ul>
          <div className='flex md:grid md:grid-cols-3 gap-x-6 overflow-x-scroll scrollbar-hide w-full'>
            <div className='min-w-full md:w-full h-[420px] rounded-[12px] overflow-hidden relative'>
              <Image
                src='/images/landing/bottom-section-thumb-1.png'
                alt='Bottom section thumb 1'
                width={0}
                height={0}
                sizes='(max-width: 768px) 100vw, 33vw'
                className='absolute top-0 left-0 w-full h-full object-cover z-0'
              />
              <HoverPlayVideo
                className='absolute top-0 left-0 w-full h-full object-cover z-0'
                src='/videos/NR-Exp-Vid1.mp4'
                loop
                muted
                playsInline
              >
                <div className='absolute bottom-20 px-10 text-white z-[1]'>
                  <p className='uppercase text-xl font-medium'>One Platform, Full Control</p>
                  <p className='italic m-0'>
                    Update once, share everywhere. Manage your rates, availability, and rental
                    details in one place and seamlessly share updates with any brokerage on
                    Nantucket—no more tedious manual updates.
                  </p>
                </div>
              </HoverPlayVideo>
            </div>
            <div className='min-w-full md:w-full h-[420px] rounded-[12px] overflow-hidden relative'>
              <Image
                src='/images/landing/bottom-section-thumb-2.png'
                alt='Bottom section thumb 2'
                width={0}
                height={0}
                sizes='(max-width: 768px) 100vw, 33vw'
                className='absolute top-0 left-0 w-full h-full object-cover z-0'
              />
              <HoverPlayVideo
                className='absolute top-0 left-0 w-full h-full object-cover z-0'
                src='/videos/NR-Exp-Vid2.mp4'
                loop
                muted
                playsInline
              >
                <div className='absolute bottom-20 px-10 text-white z-[1]'>
                  <p className='uppercase text-xl font-medium'>Organized & Efficient</p>
                  <p className='italic m-0'>
                    Ditch the spreadsheets and email chaos. Upload bookings from brokerages, Airbnb,
                    or VRBO directly into your calendar, store lease agreements for easy access, and
                    keep everything organized in one streamlined dashboard.
                  </p>
                </div>
              </HoverPlayVideo>
            </div>
            <div className='min-w-full md:w-full h-[420px] rounded-[12px] overflow-hidden relative'>
              <Image
                src='/images/landing/bottom-section-thumb-3.png'
                alt='Bottom section thumb 3'
                width={0}
                height={0}
                sizes='(max-width: 768px) 100vw, 33vw'
                className='absolute top-0 left-0 w-full h-full object-cover z-0'
              />
              <HoverPlayVideo
                className='absolute top-0 left-0 w-full h-full object-cover z-0'
                src='/videos/NR-Exp-Vid3.mp4'
                loop
                muted
                playsInline
              >
                <div className='absolute bottom-20 px-10 text-white z-[1]'>
                  <p className='uppercase text-xl font-medium'>Maximize Your Rental Potential</p>
                  <p className='italic m-0'>
                    More exposure, smarter bookings. Set your own pricing, minimum stays, and
                    seasonal availability—including overlooked months like spring and fall. Keep
                    your listing up to date with accurate details, high-quality photos, and
                    optimized booking rules.
                  </p>
                </div>
              </HoverPlayVideo>
            </div>
          </div>
        </div>
        <div className='p-[6px] md:p-2'>
          <div className='relative h-[800px] md:h-[600px] shadow-bg-video'>
            <Image
              src='/images/list-your-home-bottom.webp'
              alt='Bottom Background Image'
              width={0}
              height={0}
              sizes='100vw'
              className='w-full h-full z-[-1] rounded-[30px] bg-contain bg-no-repeat object-cover'
            />
            <div className='absolute inset-0 video-bg-overlay !bg-[rgba(0,0,0,0.35)] rounded-[30px]' />

            <div
              id='login-form'
              className='text-center absolute top-1/2 -translate-y-2/4 z-[1] left-0 right-0'
            >
              <p className='uppercase text-white tracking-[1.44px] text-xs md:text-base leading-[140%] font-bold m-0'>
                Nantucket rentals listing manager
              </p>
              <H1 className='text-white px-5 md:px-[15%] my-6 text-[32px] md:text-[50px] font-medium leading-[120%]'>
                Easily manage your vacation rental, and share updates with selected brokerages.
              </H1>
              <div className='bg-white rounded-2xl border border-solid border-[#E5E5E5] shadow-card p-6 w-[90vw] md:w-[480px] mx-auto text-left'>
                <H2 className='font-medium'>Log in or sign up</H2>
                <p className='text-sm text-gray-main'>
                  Manage your vacation rental with ease. <br />
                  Start by entering your property address.
                </p>
                <hr className='mt-4 mb-8' />
                <LoginForm />
              </div>
            </div>
          </div>
        </div>
        <div className='h-[700px] min-w-[320px] mt-10 mb-[100px] p-4 md:p-0'>
          <CalendlyWidget />
        </div>
      </main>
    </>
  );
}
