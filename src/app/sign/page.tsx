import { redirect } from 'next/navigation';

type PageProps = {
  searchParams?: { [key: string]: string | string[] | undefined };
};
export default function SignPage({ searchParams }: PageProps) {
  const signed = searchParams?.event === 'signing_complete';
  if (!signed) {
    redirect('/');
  }
  return (
    <main>
      <p className='my-10 text-success text-center text-lg md:text-2xl'>
        <PERSON><PERSON> signed successfully
      </p>
    </main>
  );
}
