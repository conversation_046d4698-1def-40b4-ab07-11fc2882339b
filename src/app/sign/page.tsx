import { redirect } from 'next/navigation';

type PageProps = {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
};
export default async function SignPage({ searchParams }: PageProps) {
  const resolvedSearchParams = await searchParams;
  const signed = resolvedSearchParams?.event === 'signing_complete';
  if (!signed) {
    redirect('/');
  }
  return (
    <main>
      <p className='my-10 text-success text-center text-lg md:text-2xl'>
        Lease signed successfully
      </p>
    </main>
  );
}
