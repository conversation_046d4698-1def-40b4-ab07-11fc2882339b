const NR_BASE_URL = process.env.NEXT_PUBLIC_NR_API_BASE_URL || '';

export type ListingAddressesPayload = {
  data: {
    street_address: string[];
  };
};

export type HomeownerProfilePayload = {
  cellphoneNumber: string;
  firstname: string;
  lastname: string;
  listingAddress: string;
  nrPmoId: number;
  userLoginEmail: string;
  assignedToInternalUser?: boolean;
  listingStatus?: string;
  nrPropertyId: number;
};

export const getListingAddresses = async <T>(query: string = '') => {
  const res = await fetch(
    `${NR_BASE_URL}/api/property/street-address/?streetAddress=${query}&includeBlocked=1`,
  );

  if (!res.ok) {
    throw new Error('Failed to fetch listing addresses');
  }

  return res.json() as T;
};

export const getHomeOwnerProfile = async <T>(address: string) => {
  const res = await fetch(
    `${NR_BASE_URL}/api/property/nrproperty-listing/homeowner-profile/?address=${encodeURIComponent(
      address,
    )}`,
  );

  console.log('res is', res);

  if (!res.ok) {
    console.error('Failed to fetch homeowner profile');
    return null;
  }

  return res.json() as T;
};
