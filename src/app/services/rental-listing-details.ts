const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getListingDetailsByNeighborhoodAndSlug = async <T>(slug: string) => {
  const res = await fetch(`${BASE_URL}/listings-by-slug/${slug}?show_ratings`, {
    next: { revalidate: 10, tags: [slug] },
  });

  if (!res.ok) {
    console.debug('Failed to fetch data', slug);
    return null;
  }

  return res.json() as T;
};

type SubmitBookingRequestData = {
  phone?: string;
  email: string;
  first_name: string;
  last_name: string;
  comment: string;
  contact_method: string;
  neighborhood: string;
  listing_id: number;
  bedroom_number?: number;
  max_price?: number;
  guest?: number;
  children?: number;
  bedrooms?: number;
  property_address: string;
  interest?: string;
  arrival_date?: string;
  departure_date?: string;
};

type ContactAgentPayload = {
  phone: string;
  email: string;
  first_name: string;
  last_name: string;
  how_can_we_help: string;
  contact_method: string;
  neighborhood: string;
  listing_id: number;
  property_address: string;
};

export const submitBookingRequest = async (data: SubmitBookingRequestData) => {
  const res = await fetch(`${BASE_URL}/get-in-touch`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Booking Request');
  }

  return res.json();
};

export const contactAgent = async (data: ContactAgentPayload) => {
  const res = await fetch(`${BASE_URL}/contact-agent`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Contact Agent Request');
  }

  return res.json();
};
