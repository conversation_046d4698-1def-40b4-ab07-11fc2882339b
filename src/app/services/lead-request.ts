const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getLeadDetails = async <T>(id: string, user: string) => {
  try {
    const res = await fetch(`${BASE_URL}/rental-opportunity-distribution/${id}?user=${user}`, {
      next: { revalidate: 0, tags: [`rental-opportunity-distribution/${id}?user=${user}`] },
    });

    return res.json() as T;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const declineLeadRequest = async (id: string, user: string) => {
  try {
    const res = await fetch(`${BASE_URL}/rental-opportunity-distribution`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        distribution_uuid: id,
        user,
        action: 'decline',
      }),
    });

    return res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const acceptLeadRequest = async (id: string, user: string) => {
  try {
    const res = await fetch(`${BASE_URL}/rental-opportunity-distribution`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        distribution_uuid: id,
        user,
        action: 'accept',
      }),
    });

    return res.json();
  } catch (error) {
    console.error(error);
    return null;
  }
};
