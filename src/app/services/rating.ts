const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const submitListingRating = async (
  listingId: number,
  data: { name: string; email: string; rating: number | null; comment?: string },
) => {
  const res = await fetch(`${BASE_URL}/listings/${listingId}/reviews`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Ratings');
  }

  return res.json();
};
