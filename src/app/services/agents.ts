const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getAgents = async <T>() => {
  const res = await fetch(`${BASE_URL}/agents`, {
    next: { revalidate: 10, tags: ['agents'] },
  });

  if (!res.ok) {
    throw new Error('Failed to fetch agents');
  }

  return res.json() as T;
};

export const getAgentDetails = async <T>(id: string) => {
  const res = await fetch(`${BASE_URL}/agents/${id}`, {
    next: { revalidate: 10, tags: ['agents'] },
  });

  if (!res.ok) {
    throw new Error('Failed to fetch agent details');
  }

  return res.json() as T;
};

type ContactAgentPMPayload = {
  phone: string;
  email: string;
  first_name: string;
  last_name: string;
  comment: string;
  contact_method: string;
  user: string | number;
};

export const contactAgentPM = async (data: ContactAgentPMPayload) => {
  const res = await fetch(`${BASE_URL}/contact-pm`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Contact Agent Request');
  }

  return res.json();
};
