const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const submitNewsletterSubscription = async (data: { email: string }) => {
  const res = await fetch(`${BASE_URL}/send-grid-contact`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Newsletter Subscription');
  }

  return res.json();
};
