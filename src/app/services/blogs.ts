export const getAllBlogs = async <T>(offset = 0) => {
  const res = await fetch(
    `https://wordpress.congdonandcoleman.com/wp-json/wp/v2/posts?orderby=date&_embed&offset=${offset}&per_page=20`,
    {
      next: { revalidate: 10, tags: [`blogs-${offset}`] },
    },
  );

  if (!res.ok) {
    throw new Error('Failed to fetch blogs');
  }

  const blogs = await res.json();

  return { blogs, count: res.headers.get('X-WP-Total') ?? 0 } as T;
};

export const getBlogBySlug = async <T>(slug: string) => {
  const res = await fetch(
    `https://wordpress.congdonandcoleman.com/wp-json/wp/v2/posts?slug=${slug}&_embed`,
    {
      next: { revalidate: 10, tags: [`blog-${slug}`] },
    },
  );

  if (!res.ok) {
    throw new Error('Failed to fetch blog details');
  }

  return res.json() as T;
};

export const getRankMathHead = async <T>(url: string) => {
  const res = await fetch(
    `https://wordpress.congdonandcoleman.com/wp-json/rankmath/v1/getHead?url=${url}`,
  );

  if (!res.ok) {
    throw new Error('Failed to fetch blog details');
  }

  return res.json() as T;
};
