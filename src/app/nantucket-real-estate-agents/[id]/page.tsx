import { EnvelopeIcon, PhoneIcon } from '@heroicons/react/24/outline';
import parse from 'html-react-parser';

import { TopBanner } from '@/app/components/common/TopBanner';
import ContactAgent from '@/app/components/nantucket-real-estate-agents/ContactAgent';
import { getAgentDetails } from '@/app/services/agents';
import { IAgent } from '@/types/agents';

import Image from 'next/image';
import Link from 'next/link';

type PageProps = {
  params: Promise<{ id: string }>;
};

const AgentDetails = async ({ params }: PageProps) => {
  const { id } = await params;
  const agent = await getAgentDetails<IAgent>(id);
  return (
    <>
      <TopBanner title='Congdon & Coleman Real Estate Agents'>
        <>
          <li>
            <Link href='/nantucket-real-estate-agents'>Agents</Link>
          </li>
          <li>{`${agent?.first_name} ${agent?.last_name}`}</li>
        </>
      </TopBanner>
      <div className='container my-10 md:my-[60px]'>
        <div className='md:border md:rounded-md md:shadow p-0 md:p-10'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10'>
            <div className='relative h-[200px] md:h-[340px] shadow-md rounded-md overflow-hidden'>
              <Image
                alt={`${agent.first_name}'s avatar`}
                src={agent.avatar ?? 'https://via.placeholder.com/300'}
                placeholder='blur'
                blurDataURL='https://via.placeholder.com/150'
                sizes='(max-width: 600px) 160px, 30vw'
                fill
                priority
                className='object-cover'
              />
            </div>
            <div>
              <p className='text-2xl md:text-4xl leading-9 md:leading-10 font-medium md-3 md:mb-4'>{`${agent?.first_name} ${agent?.last_name}`}</p>
              <p className='text-dark-3 md:text-xl leading-7 md:leading-9 mb-3 md:mb-4'>
                {agent?.title ?? 'N/A'}
              </p>
              <p className='text-dark-3 md:text-xl leading-7 md:leading-9 mb-3 md:mb-4 flex items-center'>
                <PhoneIcon className='w-6 h-6 mr-3' /> {agent?.phone ?? 'N/A'}
              </p>
              <p className='text-dark-3 md:text-xl leading-7 md:leading-9 flex items-center'>
                <EnvelopeIcon className='w-6 h-6 mr-3' /> {agent?.email ?? 'N/A'}
              </p>
              <ContactAgent agent={agent} />
            </div>
          </div>
          <div className='pt-10'>
            <p className='text-2xl md:text-4xl leading-9 md:leading-10 font-medium md-3 md:mb-[30px]'>
              About {agent.first_name}
            </p>
            <div className='text-dark-3 md:text-xl leading-7 md:leading-9 mb-[30px] md:mb-0'>
              {parse(agent.bio) ?? 'N/A'}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AgentDetails;
