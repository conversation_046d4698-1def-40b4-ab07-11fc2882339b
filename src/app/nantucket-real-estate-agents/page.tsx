import { TopBanner } from '@/app/components/common/TopBanner';
import RentalAgentCard from '@/app/components/nantucket-real-estate-agents/RentalAgentCard';
import { getAgents } from '@/app/services/agents';
import { IAgent } from '@/types/agents';
import { IPagination } from '@/types/common';

import Link from 'next/link';

const Agents = async () => {
  const { results: agents } = await getAgents<
    {
      results: IAgent[];
    } & IPagination
  >();
  return (
    <>
      <TopBanner title='Congdon & Coleman Real Estate Agents' breadcrumb='Agents' />
      <div className='container'>
        <div className='grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-5 my-8 md:my-10'>
          {agents?.map((_agent) => (
            <Link key={_agent.user_id} href={`/nantucket-real-estate-agents/${_agent.user_id}`}>
              <RentalAgentCard agent={_agent} />
            </Link>
          ))}
        </div>
      </div>
    </>
  );
};

export default Agents;
