import { CalendarIcon } from '@heroicons/react/24/outline';
import parse from 'html-react-parser';

import { getBlogBySlug, getRankMathHead } from '@/app/services/blogs';
import { Blog } from '@/types/blogs';

import dayjs from 'dayjs';
import { Manrope } from 'next/font/google';
import Head from 'next/head';

// import Image from 'next/image';
import BlogContent from '../components/BlogContent';

const manrope = Manrope({
  weight: ['200', '300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-manrope',
});

type PageProps = {
  params: Promise<{ slug: string }>;
};

export default async function BlogDetails({ params }: PageProps) {
  const { slug } = await params;
  const blogList = await getBlogBySlug<Blog[]>(slug);
  const blog = blogList?.[0];
  const rankMathHead = (await getRankMathHead<{ head: any }>(blog.link)) ?? '';
  const { _embedded } = blog;
  // const featuredImage = _embedded?.['wp:featuredmedia']?.[0]?.source_url;

  return (
    <div className={`xl:bg-[#F8F8F8] ${manrope.className}`}>
      <div className='xl:container xl:mx-auto'>
        <Head>
          <meta name='viewport' content='width=device-width,initial-scale=1' />
          {parse(rankMathHead.head ?? '')}
        </Head>

        <main className='max-w-[880px] bg-white ml-0 pb-8'>
          <div className='p-6'>
            <div className='col-span-3 md:col-span-1'>
              <h1 className='text-[24px] tracking-[0.5px] font-medium lg:text-[32px] lg:text-bold lg:w-3/4'>
                {parse(blog?.title?.rendered ?? '')}
              </h1>
              <div className='flex items-center my-2.5'>
                <p className='flex items-center mr-1 text-gray-main'>
                  <CalendarIcon className='w-4 h-4 mr-1' />
                  {dayjs(blog.date).format('MMMM DD, YYYY')}
                </p>
              </div>
            </div>
            {/* {featuredImage && (
              <div className='col-span-3 md:col-span-2'>
                <Image
                  className='featured-image'
                  src={featuredImage}
                  alt='Featured image'
                  priority
                  width={768}
                  height={360}
                />
              </div>
            )} */}
          </div>
          <div className='px-6'>
            <BlogContent content={blog.content.rendered} />
          </div>
        </main>
      </div>
    </div>
  );
}
