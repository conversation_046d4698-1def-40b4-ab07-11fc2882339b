import BlogCard from '@/app/components/blog/BlogCard';
import { Pagination } from '@/app/components/common/Pagination';
import { getAllBlogs } from '@/app/services/blogs';
import { Blog } from '@/types/blogs';

import Link from 'next/link';

export async function generateMetadata() {
  return {
    title: '<PERSON><PERSON><PERSON> & Coleman Blog',
    description: '<PERSON><PERSON>don & Coleman Blogs',
    metadataBase: new URL('https://www.congdonandcoleman.com'),
    alternates: {
      canonical: 'https://www.congdonandcoleman.com/blog/',
    },
    openGraph: {
      title: '<PERSON><PERSON><PERSON> & Coleman Blog',
      description: '<PERSON><PERSON><PERSON> & Coleman Blogs',
    },
  };
}

type PageProps = {
  searchParams: Promise<{ offset: number }>;
};

export default async function BlogList({ searchParams }: PageProps) {
  const { offset } = await searchParams;
  const { blogs, count } = await getAllBlogs<{ blogs: Blog[]; count: number }>(offset);
  const pagesCount = Math.ceil(count / 20);
  return (
    <main className='container'>
      <div className='text-center my-8 sm:my-10'>
        <h1 className='text-[46px] mb-10 font-medium'>Congdon & <PERSON> Blog</h1>
        <p className='m-auto w-10/12 text-sm text-olive-variant'>
          Nantucket, a tiny, isolated island off Cape Cod, Massachusetts, is a summer destination
          with dune-backed beaches. It’s marked by unpainted cedar-shingled buildings, many
          surrounded by manicured privets. The wharves and cobblestoned streets of the Town of
          Nantucket are lined with restaurants, high-end boutiques and steepled churches.
        </p>
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-5 mb-10 sm:mb-16'>
        {blogs.map((_b, index) => (
          <div key={index} className={index === 0 ? 'cols-span-1 sm:col-span-2' : ''}>
            <Link href={`/blog/${_b.slug}`}>
              <BlogCard blog={_b} showSummary={index === 0} />
            </Link>
          </div>
        ))}
      </div>
      {pagesCount > 1 && (
        <div className='flex justify-center lg:justify-end'>
          <Pagination queryParams={{ offset }} pageCount={pagesCount} prefix='/blog' />
        </div>
      )}
    </main>
  );
}
