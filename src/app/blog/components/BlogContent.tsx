'use client';

import parse from 'html-react-parser';

import DOMPurify from 'isomorphic-dompurify';

import '../../blog.css';

type BlogContentPropsT = {
  content: string;
};

const BlogContent = ({ content }: BlogContentPropsT) => (
  <div className='blog-content mb-10 sm:mb-16 text-base md:text-lg text-[#111111]'>
    {parse(DOMPurify.sanitize(content ?? ''))}
  </div>
);

export default BlogContent;
