import MediaView from '@/app/components/rental-listing-details/MediaView';
import PropertyDetails from '@/app/components/rental-listing-details/PropertyDetails';
import PropertyFeaturedAmenities from '@/app/components/rental-listing-details/PropertyFeaturedAmenities';
import PropertyHouseRules from '@/app/components/rental-listing-details/PropertyHouseRules';
import PropertySleepingArrangement from '@/app/components/rental-listing-details/PropertySleepingArrangement';
import { generatePropertyRentaltJsonLd } from '@/app/components/rental-listing/PropertyCard';
import { getListingDetailsByNeighborhoodAndSlug } from '@/app/services/rental-listing-details';
import MediaViewWrapper from '@/clients/components/rental-listing-details/MediaViewWrapper';
import MobileNavigationTabs from '@/clients/components/rental-listing-details/MobileNavigationTabs';
import PropertyCheckout from '@/clients/components/rental-listing-details/PropertyCheckout';
import PropertyDescription from '@/clients/components/rental-listing-details/PropertyDescription';
import PropertyLocationWrapper from '@/clients/components/rental-listing-details/PropertyLocationWrapper';
import PropertyRatesAndAvailability from '@/clients/components/rental-listing-details/PropertyRatesAndAvailability';
import StickyBookButton from '@/clients/components/rental-listing-details/StickyBookButton';
import { RentalTitle } from '@/clients/components/rental-title/RentalTitle';
import RentalDetailsContextContainer from '@/clients/contexts/RentalDetailsContext';
import { IListingDetails } from '@/types/rental-listing-details';
import { isMobileDevice } from '@/utils/responsive';

import { Metadata, ResolvingMetadata } from 'next';
import { headers } from 'next/headers';
import Image from 'next/image';
import { notFound, redirect } from 'next/navigation';
import Script from 'next/script';

export const revalidate = 600;

const MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';

type PageProps = {
  params: { slug: string | string[] };
};

export async function generateMetadata(
  { params }: PageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const formattedSlug = (params.slug as string[]).join('/') ?? '';

  const listing = await getListingDetailsByNeighborhoodAndSlug<IListingDetails>(formattedSlug);

  const previousImages = (await parent)?.openGraph?.images || [];

  // Handle case when listing is null
  if (!listing) {
    return {
      title: 'Property Not Found | Congdon & Coleman',
      description: 'The requested property could not be found.',
      metadataBase: new URL('https://www.congdonandcoleman.com'),
      robots: 'noindex,nofollow',
      openGraph: {
        images: [...previousImages],
      },
    };
  }

  return {
    title: listing.title_tag ?? '',
    description: listing.meta_description ?? '',
    metadataBase: new URL('https://www.congdonandcoleman.com'),
    alternates: {
      canonical: 'https://www.congdonandcoleman.com/nantucket-rentals/',
    },
    robots: listing.meta_robots ?? 'noindex,nofollow',
    openGraph: {
      images: [...previousImages],
    },
  };
}

export default async function RentalListingDetails({ params }: PageProps) {
  const userAgent = headers().get('user-agent');
  const isMobile = isMobileDevice(userAgent ?? '');
  const formattedSlug = (params.slug as string[]).join('/') ?? '';

  const rentalListingDetails =
    await getListingDetailsByNeighborhoodAndSlug<IListingDetails>(formattedSlug);

  if (!rentalListingDetails) {
    redirect('/nantucket-rentals/');
  }

  const propertyTitle = `${rentalListingDetails.address} | ${rentalListingDetails.area.name}`;

  return (
    <main className='container pt-10 md:pt-0 pb-2.5 md:mb-[60px] md:mt-4 md:rounded md:border'>
      <RentalTitle
        listingId={rentalListingDetails.listing_id}
        title={propertyTitle}
        avgRating={rentalListingDetails?.avg_rating}
        ratingCount={rentalListingDetails?.ratings_count}
      />
      <RentalDetailsContextContainer listingDetails={rentalListingDetails}>
        <MobileNavigationTabs />
        <div className='flex flex-col gap-3 md:gap-4 md:mt-0 md:flex-row'>
          <div className='w-full md:w-[calc(100%-300px)]'>
            <MediaViewWrapper
              virtual_tour_link={rentalListingDetails?.virtual_tour_link}
              images={rentalListingDetails.images}
              isMobile={isMobile}
            >
              <MediaView
                className='md:w-full'
                images={rentalListingDetails.images}
                isMobile={isMobile}
                virtual_tour_link={rentalListingDetails?.virtual_tour_link}
                propertyHeadline={`${rentalListingDetails.address} | ${rentalListingDetails.area.name}`}
              />
            </MediaViewWrapper>
            <PropertyDetails details={rentalListingDetails} />
            <PropertyDescription details={rentalListingDetails} />
            <PropertyHouseRules details={rentalListingDetails} />
            <PropertySleepingArrangement bedrooms={rentalListingDetails.bedrooms} />
            <PropertyFeaturedAmenities details={rentalListingDetails} />
            <PropertyRatesAndAvailability details={rentalListingDetails} isMobile={isMobile} />
          </div>
          <div className='w-full md:w-[300px]'>
            <PropertyCheckout details={rentalListingDetails} isMobile={isMobile} />
            <PropertyLocationWrapper>
              <Image
                alt='Map placeholder'
                className='h-auto w-full md:w-[300px]'
                height={0}
                width={0}
                sizes='300px'
                src={`https://maps.googleapis.com/maps/api/staticmap?center=${
                  rentalListingDetails?.latitude ?? 41.2835
                },${
                  rentalListingDetails?.longitude ?? -70.0995
                }&zoom=16&size=400x400&markers=color:red%7C${
                  rentalListingDetails?.latitude ?? 41.2835
                },${rentalListingDetails?.longitude ?? -70.0995}&key=${MAPS_API_KEY}`}
              />
            </PropertyLocationWrapper>
          </div>
        </div>
        <StickyBookButton />
      </RentalDetailsContextContainer>
      <Script
        id={`rental-${rentalListingDetails.slug.split(' ').join().toLocaleLowerCase()}`}
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generatePropertyRentaltJsonLd(rentalListingDetails)),
        }}
      />
    </main>
  );
}
