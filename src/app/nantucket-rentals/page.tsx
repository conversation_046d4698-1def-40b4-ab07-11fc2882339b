import { Pagination } from '@/app/components/common/Pagination';
import { TopBanner } from '@/app/components/common/TopBanner';
import { PropertyCard } from '@/app/components/rental-listing/PropertyCard';
import RentalTopFiltersWrapper from '@/app/components/rental-listing/RentalTopFiltersWrapper';
import { getRentalSearchQuery, getSearchParams } from '@/app/utils';
import ClientWrapper from '@/clients/components/nantucket-rentals/ClientWrapper';
import RentalTopFilters from '@/clients/components/rentals-top-filters/RentalTopFilters';
import { TopFilterType } from '@/types/common';

import { Metadata } from 'next';

export const revalidate = 600;

type MetaDataProps = {
  title_tag: string;
  meta_description: string;
  meta_robots: string;
  meta_canonical: string;
};

const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

export async function generateMetadata(): Promise<Metadata> {
  const seo = (await fetch(`${baseUrl}/seo-metadata/nantucket_rentals?show_ratings`).then((res) =>
    res.json(),
  )) as MetaDataProps;
  return {
    title: seo.title_tag,
    description: seo.meta_description,
    alternates: {
      canonical: seo.meta_canonical,
    },
  };
}

const perPage = 32;

async function getData(searchParams: any) {
  const sp = Object.assign(
    {
      limit: perPage,
      offset: 0,
      show_price: true,
      ordering: 'priority,-calendar_updated_at',
      show_ratings: 1,
    },
    searchParams,
  );
  const params = getSearchParams(getRentalSearchQuery(sp));
  console.log('url', `${baseUrl}/listings?` + params);
  const rentals = await fetch(`${baseUrl}/listings?` + params);
  return {
    rentals: await rentals.json(),
  };
}

export default async function RentalListing({
  searchParams,
}: {
  searchParams: Promise<{ offset: number } & { [key: string]: string | undefined }>;
}) {
  const resolvedSearchParams = await searchParams;
  const { rentals } = await getData(resolvedSearchParams);
  const pagesCount = Math.ceil(rentals.count / perPage);

  const currentPage = Math.ceil(resolvedSearchParams.offset ?? 0 / perPage) + 1;
  const totalPage = Math.min(Math.min(rentals.count, perPage) + currentPage - 1, rentals.count);
  const filterTitle =
    rentals.count > 0 ? `Showing ${currentPage}-${totalPage} of ${rentals.count} listings` : '';

  const sortByPrice = resolvedSearchParams?.ordering?.includes('peak_rate');

  return (
    <div>
      <ClientWrapper rentalListings={rentals.results} searchParams={resolvedSearchParams}>
        <TopBanner title='Nantucket Vacation and Summer Rentals' breadcrumb='Rent' />
        <div className='xl:container'>
          <div className='px-4 pb-14 pt-10'>
            <RentalTopFiltersWrapper>
              <RentalTopFilters
                title={filterTitle}
                propertyCount={rentals?.count}
                filterType={TopFilterType.RENTALS}
              />
            </RentalTopFiltersWrapper>
            <div className='gap-x-3 gap-5 pb-10 grid grid-cols-1 md:gap-x-5 md:gap-y-7.5 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {rentals?.results?.map((property: any, index: number) => (
                <PropertyCard
                  key={property.listing_id}
                  property={property}
                  preload={index === 0}
                  sortByPrice={sortByPrice}
                />
              ))}
            </div>
            {pagesCount > 1 && (
              <div className='flex justify-center lg:justify-end'>
                <Pagination
                  queryParams={resolvedSearchParams}
                  pageCount={pagesCount}
                  prefix='/nantucket-rentals'
                />
              </div>
            )}
          </div>
        </div>
      </ClientWrapper>
    </div>
  );
}
