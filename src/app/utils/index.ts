import { isArray } from 'lodash';

import dayjs from 'dayjs';

import { RealEstatePropertyProps } from '../components/real-estate/PropertyCard';
import { MlsStatus } from '../types';

export const getSearchParams = (searchParams: Record<string, string | number>) => {
  const params = [];
  for (const [key, value] of Object.entries(searchParams)) {
    if (isArray(value)) value.forEach((x) => params.push(key + '=' + x));
    else params.push(key + '=' + value);
  }
  const str = params.join('&');
  return str;
};

export const getRentalSearchQuery = (formValues: any) => {
  const numberOfDays = parseInt(formValues.nights ?? '7');
  const output = Object.entries(formValues)
    .filter(
      ([_, value]) =>
        _ !== 'nights' && (Array.isArray(value) ? value.length > 0 : value && value !== null),
    )
    .reduce(
      (acc, [key, value]) =>
        key === 'checkin_date'
          ? {
              ...acc,
              start_date: value,
              end_date: dayjs(value as string)
                .add(numberOfDays, 'days')
                .format('YYYY-MM-DD'),
            }
          : { ...acc, [key]: value },
      {},
    );

  return output;
};

export const getUnavailablePropertyTypeLabel = (status: MlsStatus) => {
  const labelMap: Record<'U' | 'S', string> = {
    U: 'Under agreement',
    S: 'Sold',
  };

  return status === 'U' || status === 'S' ? labelMap[status] : ''
}

export const getPropertyTypeLabel = (type: RealEstatePropertyProps['PropertyType']) => {
  const toLower = type.toLowerCase();
  return toLower.includes('land')
    ? 'Land for Sale'
    : toLower.includes('commercial')
    ? 'Commercial Property for Sale'
    : 'House for Sale';
};
