import { memo } from 'react';

import { CalendarIcon } from '@heroicons/react/24/outline';
import parse from 'html-react-parser';

import Button from '@/clients/ui/button';
import { Blog } from '@/types/blogs';

import dayjs from 'dayjs';
import Image from 'next/image';

type Props = {
  blog: Blog;
  showSummary?: boolean;
};

const BlogCard = ({ blog, showSummary }: Props) => {
  const { _embedded } = blog;
  const featuredImage = _embedded['wp:featuredmedia']?.[0]?.source_url;
  return (
    <div className='w-full h-full rounded shadow-md overflow-hidden'>
      <div className='relative h-[250px]'>
        {featuredImage ? (
          <Image
            src={featuredImage}
            alt='card-image'
            loading={showSummary ? 'eager' : 'lazy'}
            fill
            sizes='320px'
            className='object-cover object-center'
            placeholder='blur'
            blurDataURL='https://via.placeholder.com/150'
          />
        ) : (
          <div className='bg-[#808080] w-full h-full' />
        )}
      </div>
      <div className='px-4 py-5'>
        <div className='flex items-center mb-2.5'>
          <p className='text-gray-main flex items-center text-xs flex-grow-1'>
            <CalendarIcon className='w-3 h-3 mr-1' />
            {dayjs(blog.date).format('MMMM DD, YYYY')}
          </p>
        </div>
        <h5>{parse(blog?.title?.rendered ?? '')}</h5>
        {showSummary ? (
          <div className='text-gray-main'>{parse(blog?.excerpt?.rendered ?? '')}</div>
        ) : (
          <div className='flex items-center justify-center mt-8'>
            <Button color='info'>Read More</Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(BlogCard);
