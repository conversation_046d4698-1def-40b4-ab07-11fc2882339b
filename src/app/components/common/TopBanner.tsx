import { ReactElement } from 'react';

import { Icon } from '@/clients/components/icon';

type TopBannerProps = {
  title: string;
  breadcrumb?: string;
  children?: ReactElement<HTMLLIElement>;
};

export const TopBanner = ({ title, breadcrumb = '', children }: TopBannerProps) => (
  <div className='flex h-52 flex-col items-center justify-center gap-y-5 bg-[#E5F1F4] px-10 text-center'>
    <h1 className='text-2xl font-semibold lg:text-4xl'>{title}</h1>
    <div className='breadcrumbs'>
      <ol>
        <li>
          <a className='flex gap-x-2.5' href='https://www.congdonandcoleman.com'>
            <Icon icon='home' />
            <span>Home</span>
          </a>
        </li>
        {breadcrumb && <li>{breadcrumb}</li>}
        {children}
      </ol>
    </div>
  </div>
);
