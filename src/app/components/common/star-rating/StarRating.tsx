'use client';

export type Rating = 0 | 0.5 | 1 | 1.5 | 2 | 2.5 | 3 | 3.5 | 4 | 4.5 | 5;

type StarRatingProps = {
  rating?: Rating | null;
  onRate: (rate: Rating) => void;
  readOnly?: boolean;
};

const MAX_RATE = 5;

const StarRating = ({ rating, readOnly = false, onRate }: StarRatingProps) => {
  const stars = Array(MAX_RATE * 2)
    .fill({ id: -1, checked: false })
    .map((_, index) => {
      const starRate = ((index + 1) / 2) as Rating;
      const isChecked = starRate === rating;

      return {
        id: starRate,
        checked: isChecked,
      };
    });

  const onChangeRate = (starId: Rating) => {
    onRate(starId);
  };

  return (
    <div className='flex md:self-end rating rating-half'>
      <div className='rating rating-half' title='click to submit a rate' aria-hidden>
        {stars.map((star, index) => {
          const isEven = index % 2 == 0;
          const maskClass = `${isEven ? 'mask-half-1' : 'mask-half-2'}`;

          return (
            <input
              key={star.id}
              type='radio'
              name='rating-10'
              className={`${
                star.checked || rating ? 'bg-opacity-100' : 'bg-opacity-20'
              } bg-cyan-900 mask mask-star-2 ${maskClass}`}
              onClick={() => {
                if (!readOnly) {
                  onChangeRate?.(star.id);
                }
              }}
              onChange={() => {
                if (!readOnly) {
                  onChangeRate?.(star.id);
                }
              }}
              checked={star.checked}
            />
          );
        })}
      </div>
    </div>
  );
};

export default StarRating;
