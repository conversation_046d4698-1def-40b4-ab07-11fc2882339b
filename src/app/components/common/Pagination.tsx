import { getSearchParams } from '@/app/utils';
import { Icon } from '@/clients/components/icon';

import Link from 'next/link';

type PaginationProps = {
  prefix: string;
  queryParams: {
    offset: number;
  };
  pageCount: number;
};

export const Pagination = ({ queryParams, prefix, pageCount }: PaginationProps) => {
  const page = queryParams?.offset > 0 ? Math.floor(queryParams?.offset / 20) + 1 : 1;

  let left = 0;
  let right = 0;
  if (pageCount >= 7) {
    if (page > 5 && page < pageCount - 4) {
      left = Number(page) - 2;
      right = Number(page) + 2;
    } else if (page <= 5) {
      left = 1;
      right = 7;
    } else {
      right = pageCount;
      left = pageCount - 6;
    }
  }

  const shouldShowLeftEllipsis = () => {
    if (pageCount <= 7) {
      return false;
    }
    return page > 5;
  };

  const shouldShowRightEllipsis = () => {
    if (pageCount <= 7) {
      return false;
    }
    return left !== pageCount && right !== pageCount;
  };

  const ellipsis = (
    <span className='flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200'>
      ...
    </span>
  );

  const ltOneToLast = page - 1 < pageCount;
  const isLast = page === pageCount;
  const selectedClass = 'bg-cyan-900 border-cyan-900 text-white';

  return (
    <div className='mb-4 flex flex-wrap gap-1.5'>
      <Link
        href={
          `${prefix}/?` +
          getSearchParams({
            ...queryParams,
            offset: 20 * (page - 2 < 0 ? 0 : page - 2),
          })
        }
        scroll={false}
        aria-label='move back one page'
        className='flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200'
      >
        <Icon icon='arrow-left' />
      </Link>
      {page > 5 && (
        <Link
          href={
            `${prefix}/?` +
            getSearchParams({
              ...queryParams,
              offset: 0,
            })
          }
          scroll={false}
          aria-label='move back to the first page'
          className='flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200'
        >
          1
        </Link>
      )}
      {shouldShowLeftEllipsis() && ellipsis}
      {Array(pageCount)
        .fill(null)
        .map((x, i) => {
          if (left === 0 && right === 0) i++;
          if ((i >= left && i < right) || (left === 0 && right === 0)) {
            const className = page == i ? selectedClass : 'border-zinc-200 text-navy-blue';
            const urlLink =
              `${prefix}/?` +
              getSearchParams({
                ...queryParams,
                offset: 20 * (i - 1),
              });
            return (
              <Link
                key={urlLink}
                href={urlLink}
                aria-label={`move to page ${i}`}
                scroll={false}
                className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded border ${className}`}
              >
                <span className='text-center font-medium text-inherit'>{i}</span>
              </Link>
            );
          }
        })}
      {shouldShowRightEllipsis() && ellipsis}
      {page > 4 && ltOneToLast && (
        <Link
          href={
            `${prefix}/?` +
            getSearchParams({
              ...queryParams,
              offset: 20 * (pageCount - 1),
            })
          }
          scroll={false}
          aria-label='move to the last page'
          className={`flex h-9 w-9 items-center justify-center rounded border ${
            isLast ? selectedClass : 'border-zinc-200 '
          }`}
        >
          <span className='text-center font-medium text-inherit'>{pageCount}</span>
        </Link>
      )}
      <Link
        href={
          `${prefix}/?` +
          getSearchParams({
            ...queryParams,
            offset: 20 * page,
          })
        }
        scroll={false}
        aria-label='move to the next page'
        className='flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-zinc-200'
      >
        <Icon icon='arrow-right' />
      </Link>
    </div>
  );
};
