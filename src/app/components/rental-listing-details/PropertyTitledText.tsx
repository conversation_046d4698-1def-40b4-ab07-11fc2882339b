import { twMerge } from 'tailwind-merge';

type Props = {
  title: string;
  value?: string | number;
  className?: string;
};

const PropertyTitledText = ({ title, value, className }: Props) => {
  return (
    <div className={twMerge('mb-5', className)}>
      <p className='mb-2.5 text-sm leading-[130%] text-gray-700 opacity-80'>{title}</p>
      <p className='mt-1 text-sm font-medium leading-[120%] text-gray-700'>{value}</p>
    </div>
  );
};

export default PropertyTitledText;
