import React from 'react';

import AmenityItem from '@/app/components/rental-listing-details/AmenityItem';
import { IListingDetails } from '@/types/rental-listing-details';
import { getAmenityIconSrcPath } from '@/utils/amenity';

type Props = {
  details: IListingDetails;
};

const BedroomsAndBathrooms = ({ details }: Props) => {
  return (
    <div>
      <p className='mb-2.5 font-medium leading-[175%]'>Bedrooms & Bathrooms -</p>
      <AmenityItem
        title='Sleeping Capacity'
        iconPath={getAmenityIconSrcPath('sleeping_capacity')}
        quantity={details.capacity ?? 0}
        className='mb-5'
      />
      <AmenityItem
        title='Total Bedrooms'
        iconPath={getAmenityIconSrcPath('dining_area')}
        quantity={details.bedroom_number ?? 0}
        className='mb-5'
      />
      <AmenityItem
        title='Total Bathrooms'
        iconPath={getAmenityIconSrcPath('dining_area')}
        type={`${details.bathroom_number} full${
          (details?.half_bathroom_number ?? 0) > 0 ? `, ${details.half_bathroom_number} half` : ``
        }`}
        className='mb-5'
      />
    </div>
  );
};

export default BedroomsAndBathrooms;
