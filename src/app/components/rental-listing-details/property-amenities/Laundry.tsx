import React from 'react';

import AmenityItem from '@/app/components/rental-listing-details/AmenityItem';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  getAmenityIconSrcPath,
  getAmenityQuantity,
  getAmenityTitle,
  getAmenityType,
} from '@/utils/amenity';

type Props = {
  details: IListingDetails;
};

const LAUNDRY_AMENITY_KEYS = [
  'clothes_dryer',
  'iron',
  'ironing_board',
  'washer',
  'dryer',
  'washing_machine',
];

const Laundry = ({ details }: Props) => {
  return (
    <div>
      <p className='mb-2.5 font-medium leading-[175%]'>Laundry -</p>
      {LAUNDRY_AMENITY_KEYS.map(
        (_key, index) =>
          details?.[_key as unknown as keyof IListingDetails] && (
            <AmenityItem
              key={index}
              title={getAmenityTitle(_key)}
              iconPath={getAmenityIconSrcPath(_key)}
              quantity={getAmenityQuantity(_key, details)}
              type={getAmenityType(_key, details)}
              className='mb-5'
            />
          ),
      )}
    </div>
  );
};

export default Laundry;
