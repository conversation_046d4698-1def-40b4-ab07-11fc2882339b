import React from 'react';

import { IListingBedroom } from '@/types/rental-listing-details';

type Props = {
  bedrooms: IListingBedroom[];
};

const PropertySleepingArrangement = ({ bedrooms }: Props) => {
  return (
    <div className='my-5 border-b'>
      <p className='mb-5 text-lg font-medium leading-[175%]'>Sleeping Arrangements</p>
      {bedrooms.map((_bedroom, index) => (
        <div key={index} className='mb-2.5 flex items-start'>
          <p className='mr-2.5 w-4/12 md:w-[160px] font-medium'>Bedroom {index + 1}</p>
          <div className='mr-2.5 w-4/12 md:w-[160px] text-sm text-gray-main'>
            {_bedroom.beds.map((_bed) => (
              <div key={_bed.bed_uuid} className='mr-2'>
                {_bed.type.name} {_bed.number > 1 && `(${_bed.number})`}
              </div>
            ))}
          </div>
          {_bedroom?.floor_level && (
            <p className='mr-2.5 w-4/12 md:w-[160px] text-sm text-gray-main'>
              {_bedroom?.floor_level?.name ?? ''}
            </p>
          )}
        </div>
      ))}
    </div>
  );
};

export default PropertySleepingArrangement;
