import ShareMenu from '@/clients/components/share-menu';
import { IListingDetails } from '@/types/rental-listing-details';

type Props = {
  details: IListingDetails;
};
const prefix = process.env.VERCEL_ENV === 'production' ? 'https://www' : 'https://dev';

const PropertyDetails = ({ details }: Props) => {
  return (
    <div className='my-5 border-b'>
      <div>
        <h2 className=' mt-2.5 text-lg font-medium md:hidden'>{details.address}</h2>
      </div>
      <div className='mb-5 flex items-center justify-between border-b pb-2.5 md:border-b-0'>
        <p className='hidden text-lg font-medium leading-[175%] md:block'>About this Property</p>
        <p className='font-medium leading-[175%] text-olive-variant md:hidden'>
          {details.area.name}
        </p>
        <ShareMenu
          title='Share this property'
          shareURL={`${prefix}.congdonandcoleman.com/nantucket-rentals/${details.slug}`}
          shareText='Look at this property I found on Congdon & Coleman!'
        />
      </div>
      <div className='grid grid-cols-1 md:grid-cols-3 md:gap-4'>
        <div className='mb-4 flex items-center justify-between md:mb-5 md:block'>
          <p className='text-sm leading-[130%] text-gray-700 opacity-80 md:mb-2.5'>Turnover</p>
          <p className='text-sm font-medium leading-[120%] text-gray-700 md:mt-1'>
            {details.requirement.turnover_day ?? '-'}
          </p>
        </div>
        <div className='mb-4 flex items-center justify-between md:mb-5 md:block'>
          <p className='text-sm leading-[130%] text-gray-700 opacity-80 md:mb-2.5'>
            Sleeping capacity
          </p>
          <p className='text-sm font-medium leading-[120%] text-gray-700 md:mt-1'>
            {details.capacity ?? 0}
          </p>
        </div>
        <div className='mb-4 flex items-center justify-between md:mb-5 md:block'>
          <p className='text-sm leading-[130%] text-gray-700 opacity-80 md:mb-2.5'>Bedrooms</p>
          <p className='text-sm font-medium leading-[120%] text-gray-700 md:mt-1'>
            {details.bedroom_number ?? 0}
          </p>
        </div>
        <div className='mb-4 flex items-center justify-between md:mb-5 md:block'>
          <p className='text-sm leading-[130%] text-gray-700 opacity-80 md:mb-2.5'>Bathrooms</p>
          <p className='text-sm font-medium leading-[120%] text-gray-700 md:mt-1'>
            {`${details.bathroom_number} full${
              (details?.half_bathroom_number ?? 0) > 0
                ? `, ${details.half_bathroom_number} half`
                : ``
            }`}
          </p>
        </div>
        <div className='mb-4 flex items-center justify-between md:mb-5 md:block'>
          <p className='text-sm leading-[130%] text-gray-700 opacity-80 md:mb-2.5'>
            To Center of Town
          </p>
          <p className='text-sm font-medium leading-[120%] text-gray-700 md:mt-1'>
            {details.distance_to_the_hub ?? '-'}
          </p>
        </div>
        <div className='mb-4 flex items-center justify-between md:mb-5 md:block'>
          <p className='text-sm leading-[130%] text-gray-700 opacity-80 md:mb-2.5'>
            To Nearest Beach
          </p>
          <p className='text-sm font-medium leading-[120%] text-gray-700 md:mt-1'>
            {details.distance_to_beach ?? '-'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetails;
