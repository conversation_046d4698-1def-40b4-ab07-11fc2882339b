import ViewAllAmenities from '@/clients/components/rental-listing-details/ViewAllAmenities';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  getAmenityIconSrcPath,
  getAmenityQuantity,
  getAmenityTitle,
  getAmenityType,
} from '@/utils/amenity';

import AmenityItem from './AmenityItem';
import BedroomsAndBathrooms from './property-amenities/BedroomsAndBathrooms';
import Entertainment from './property-amenities/Entertainment';
import Essentials from './property-amenities/Essentials';
import KitchenAndDining from './property-amenities/KitchenAndDining';
import Laundry from './property-amenities/Laundry';
import Outdoors from './property-amenities/Outdoors';

type Props = {
  details: IListingDetails;
};

const PropertyFeaturedAmenities = ({ details }: Props) => {
  return (
    <div className='my-5 border-b'>
      <p className='mb-5 text-lg font-medium leading-[175%]'>Featured Amenities</p>
      <div className='mb-5 grid grid-cols-2 gap-4 md:grid-cols-3'>
        {details.featured_amenities?.map((_amenity, index) => (
          <AmenityItem
            key={index}
            title={getAmenityTitle(_amenity)}
            quantity={getAmenityQuantity(_amenity, details)}
            type={getAmenityType(_amenity === 'air_conditioning' ? 'ac' : _amenity, details)}
            iconPath={getAmenityIconSrcPath(_amenity)}
          />
        ))}
      </div>
      <ViewAllAmenities
        allAmenitiesNode={
          <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
            <Essentials details={details} />
            <Outdoors details={details} />
            <BedroomsAndBathrooms details={details} />
            <KitchenAndDining details={details} />
            <Entertainment details={details} />
            <Laundry details={details} />
          </div>
        }
      />
    </div>
  );
};

export default PropertyFeaturedAmenities;
