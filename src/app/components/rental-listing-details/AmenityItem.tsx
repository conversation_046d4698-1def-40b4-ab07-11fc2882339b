import React from 'react';

import { IconIdentifier, getIconSrcPath } from '@/utils/icon';

import Image from 'next/image';
import { twMerge } from 'tailwind-merge';

type Props = {
  title: string;
  iconIdentifier?: IconIdentifier;
  className?: string;
  iconPath?: string;
  quantity?: number;
  type?: string;
};

const AmenityItem = ({
  title,
  iconIdentifier,
  className,
  iconPath = '',
  quantity = 0,
  type,
}: Props) => {
  return (
    <div className={twMerge('flex items-center text-sm text-gray-main ', className)}>
      <Image
        src={iconPath ? iconPath : getIconSrcPath(iconIdentifier)}
        alt='amenity icon'
        width={0}
        height={0}
        loading='lazy'
        className='mr-2.5 h-auto w-4'
      />
      {title} {quantity > 0 ? `(${quantity})` : ``}
      {type ? `(${type})` : ``}
    </div>
  );
};

export default AmenityItem;
