import React from 'react';

import { IListingDetails } from '@/types/rental-listing-details';

import PropertyTitledText from './PropertyTitledText';

type Props = {
  details: IListingDetails;
};

const convertTo12hours = (timeString: string) =>
  new Date('1970-01-01T' + timeString + 'Z').toLocaleTimeString('en-US', {
    timeZone: 'UTC',
    hour12: true,
    hour: 'numeric',
    minute: 'numeric',
  });

const PropertyHouseRules = ({ details }: Props) => {
  return (
    <div className='my-5 border-b'>
      <p className='mb-5 text-lg font-medium leading-[175%]'>House Rules</p>
      <div className='grid grid-cols-2 gap-3 md:grid-cols-4'>
        <PropertyTitledText
          title='Check-in'
          value={convertTo12hours(details.requirement.checkin_time)}
        />
        <PropertyTitledText
          title='Check-out'
          value={convertTo12hours(details.requirement.checkout_time)}
        />
        <PropertyTitledText title='Max. Overnight Guests' value={`${details.capacity} people`} />
        <PropertyTitledText
          title='Pet Policy'
          value={details.requirement.pet_allow === 'true' ? 'Pets Allowed' : 'No Pets Allowed'}
        />
      </div>
    </div>
  );
};

export default PropertyHouseRules;
