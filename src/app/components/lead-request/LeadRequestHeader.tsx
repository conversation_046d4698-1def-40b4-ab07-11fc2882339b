import CountdownTimer from '@/clients/components/lead-request/CountdownTimer';
import { Nullable } from '@/types/common';
import { Lead } from '@/types/lead';

import classNames from 'classnames';

type Props = {
  expired: boolean;
  accepted: boolean;
  declined: boolean;
  leadDetails?: Nullable<Lead>;
};

const LeadRequestHeader = ({ expired, accepted, declined, leadDetails }: Props) => {
  const expiresAt = new Date(leadDetails?.expires_at ?? 0);
  const initialSeconds = Math.floor((expiresAt.getTime() - Date.now()) / 1000);

  return (
    <div className='bg-[#E5F1F4] p-4 w-full'>
      <div className='px-5 py-5 md:px-0 w-full md:w-[400px] text-center mx-auto'>
        <p className='text-2xl font-bold'>
          {accepted ? 'Lead Accepted' : declined ? 'Lead Declined' : 'New Lead'}
        </p>
        <p
          className={classNames('text-sm text-[#1C4A5E] font-medium', {
            'mt-4': accepted || declined,
          })}
        >
          {accepted
            ? `Follow up with the lead and let them you are working on their inquiry.`
            : declined
            ? `This lead has been offered to another agent.`
            : `Exclusively yours for`}
        </p>
        {!accepted && !declined && (
          <div className='flex items-center justify-center gap-x-2'>
            <svg
              width='34'
              height='34'
              viewBox='0 0 34 34'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M16.9999 1.92664C16.374 1.92664 15.8666 2.43404 15.8666 3.05998V7.98399C15.8666 8.60991 16.374 9.11733 16.9999 9.11733C17.6259 9.11733 18.1333 8.60991 18.1333 7.98399V4.24277C24.6754 4.81642 29.8067 10.309 29.8067 17C29.8067 24.0729 24.0729 29.8067 16.9999 29.8067C9.92702 29.8067 4.19328 24.0729 4.19328 17C4.19328 13.8406 5.33588 10.9509 7.23177 8.7174C7.63682 8.24022 7.57834 7.52502 7.10114 7.11996C6.62396 6.71491 5.90876 6.77339 5.50371 7.25057C3.27317 9.87834 1.92661 13.2833 1.92661 17C1.92661 25.3248 8.67516 32.0734 16.9999 32.0734C25.3248 32.0734 32.0733 25.3248 32.0733 17C32.0733 8.67519 25.3248 1.92664 16.9999 1.92664ZM15.2784 18.3163L9.57355 10.3641C9.41189 10.1387 9.43717 9.82947 9.63328 9.63336C9.82937 9.43724 10.1386 9.41197 10.364 9.57363L18.3162 15.2785C19.4078 16.0616 19.5365 17.6368 18.5866 18.5867C17.6367 19.5366 16.0615 19.4079 15.2784 18.3163Z'
                fill='black'
              />
            </svg>
            {expired ? (
              <span className='text-[32px] font-bold'>00:00</span>
            ) : (
              <CountdownTimer initialSeconds={initialSeconds} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default LeadRequestHeader;
