import { Lead } from '@/types/lead';

import LeadRequestDetails from './LeadRequestDetails';

const LeadAcceptedDetails = ({ leadDetails }: { leadDetails: Lead }) => {
  return (
    <>
      <div className='flex items-center justify-between mx-6'>
        <a href={`tel:${leadDetails.contact.cell_phone}`}>
          <div className='cursor-pointer'>
            <svg
              width='60'
              height='60'
              viewBox='0 0 60 60'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <g clipPath='url(#clip0_7000_70195)'>
                <path
                  d='M54.925 45.825C54.925 46.725 54.725 47.65 54.3 48.55C53.875 49.45 53.325 50.3 52.6 51.1C51.375 52.45 50.025 53.425 48.5 54.05C47 54.675 45.375 55 43.625 55C41.075 55 38.35 54.4 35.475 53.175C32.6 51.95 29.725 50.3 26.875 48.225C24 46.125 21.275 43.8 18.675 41.225C16.1 38.625 13.775 35.9 11.7 33.05C9.65 30.2 8 27.35 6.8 24.525C5.6 21.675 5 18.95 5 16.35C5 14.65 5.3 13.025 5.9 11.525C6.5 10 7.45 8.6 8.775 7.35C10.375 5.775 12.125 5 13.975 5C14.675 5 15.375 5.15 16 5.45C16.65 5.75 17.225 6.2 17.675 6.85L23.475 15.025C23.925 15.65 24.25 16.225 24.475 16.775C24.7 17.3 24.825 17.825 24.825 18.3C24.825 18.9 24.65 19.5 24.3 20.075C23.975 20.65 23.5 21.25 22.9 21.85L21 23.825C20.725 24.1 20.6 24.425 20.6 24.825C20.6 25.025 20.625 25.2 20.675 25.4C20.75 25.6 20.825 25.75 20.875 25.9C21.325 26.725 22.1 27.8 23.2 29.1C24.325 30.4 25.525 31.725 26.825 33.05C28.175 34.375 29.475 35.6 30.8 36.725C32.1 37.825 33.175 38.575 34.025 39.025C34.15 39.075 34.3 39.15 34.475 39.225C34.675 39.3 34.875 39.325 35.1 39.325C35.525 39.325 35.85 39.175 36.125 38.9L38.025 37.025C38.65 36.4 39.25 35.925 39.825 35.625C40.4 35.275 40.975 35.1 41.6 35.1C42.075 35.1 42.575 35.2 43.125 35.425C43.675 35.65 44.25 35.975 44.875 36.4L53.15 42.275C53.8 42.725 54.25 43.25 54.525 43.875C54.775 44.5 54.925 45.125 54.925 45.825Z'
                  stroke='#4C737F'
                  strokeWidth='1.5'
                  strokeMiterlimit='10'
                />
              </g>
              <defs>
                <clipPath id='clip0_7000_70195'>
                  <rect width='60' height='60' fill='white' />
                </clipPath>
              </defs>
            </svg>
          </div>
        </a>
        <a href={`sms:${leadDetails.contact.cell_phone}`}>
          <div className='cursor-pointer'>
            <svg
              width='60'
              height='60'
              viewBox='0 0 60 60'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                opacity='0.4'
                d='M42.5 51.25H17.5C10 51.25 5 47.5 5 38.75V21.25C5 12.5 10 8.75 17.5 8.75H42.5C50 8.75 55 12.5 55 21.25V38.75C55 47.5 50 51.25 42.5 51.25Z'
                fill='#4C737F'
              />
              <path
                d='M30.0001 32.175C27.9001 32.175 25.7751 31.525 24.1501 30.2L16.3251 23.95C15.5251 23.3 15.3751 22.125 16.0251 21.325C16.6751 20.525 17.8501 20.375 18.6501 21.025L26.475 27.275C28.375 28.8 31.6 28.8 33.5 27.275L41.3251 21.025C42.1251 20.375 43.3251 20.5 43.9501 21.325C44.6001 22.125 44.4751 23.325 43.6501 23.95L35.8251 30.2C34.2251 31.525 32.1001 32.175 30.0001 32.175Z'
                fill='#4C737F'
              />
            </svg>
          </div>
        </a>
        <a href={`mailto:${leadDetails.contact.email}`}>
          <div className='cursor-pointer'>
            <svg
              width='60'
              height='60'
              viewBox='0 0 60 60'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                opacity='0.4'
                d='M44.95 26.975V36.975C44.95 37.625 44.925 38.25 44.85 38.85C44.275 45.6 40.3 48.95 32.975 48.95H31.975C31.35 48.95 30.75 49.25 30.375 49.75L27.375 53.75C26.05 55.525 23.9 55.525 22.575 53.75L19.575 49.75C19.25 49.325 18.525 48.95 17.975 48.95H16.975C9.00002 48.95 5 46.975 5 36.975V26.975C5 19.65 8.37502 15.675 15.1 15.1C15.7 15.025 16.325 15 16.975 15H32.975C40.95 15 44.95 19 44.95 26.975Z'
                fill='#4C737F'
              />
              <path
                d='M24.975 35C23.575 35 22.475 33.875 22.475 32.5C22.475 31.125 23.6 30 24.975 30C26.35 30 27.475 31.125 27.475 32.5C27.475 33.875 26.375 35 24.975 35Z'
                fill='#4C737F'
              />
              <path
                d='M33.725 35C32.325 35 31.225 33.875 31.225 32.5C31.225 31.125 32.35 30 33.725 30C35.1 30 36.225 31.125 36.225 32.5C36.225 33.875 35.1 35 33.725 35Z'
                fill='#4C737F'
              />
              <path
                d='M16.25 35C14.85 35 13.75 33.875 13.75 32.5C13.75 31.125 14.875 30 16.25 30C17.625 30 18.75 31.125 18.75 32.5C18.75 33.875 17.625 35 16.25 35Z'
                fill='#4C737F'
              />
              <path
                d='M54.95 16.975V26.975C54.95 34.325 51.575 38.275 44.85 38.85C44.925 38.25 44.95 37.625 44.95 36.975V26.975C44.95 19 40.95 15 32.975 15H16.975C16.325 15 15.7 15.025 15.1 15.1C15.675 8.37502 19.65 5 26.975 5H42.975C50.95 5 54.95 9.00002 54.95 16.975Z'
                fill='#4C737F'
              />
            </svg>
          </div>
        </a>
      </div>
      <hr className='my-4 border-[#D8E2E4] border-t-[1.5px]' />
      <p className='text-xl font-semibold'>
        {leadDetails.contact.first_name + ' ' + leadDetails.contact.last_name}
      </p>
      <div className='flex items-center gap-x-2 my-2'>
        <svg
          width='16'
          height='16'
          viewBox='0 0 16 16'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M14.6466 12.22C14.6466 12.46 14.5933 12.7067 14.48 12.9467C14.3666 13.1867 14.22 13.4134 14.0266 13.6267C13.7 13.9867 13.34 14.2467 12.9333 14.4134C12.5333 14.58 12.1 14.6667 11.6333 14.6667C10.9533 14.6667 10.2266 14.5067 9.45998 14.18C8.69331 13.8534 7.92665 13.4134 7.16665 12.86C6.39998 12.3 5.67331 11.68 4.97998 10.9934C4.29331 10.3 3.67331 9.57337 3.11998 8.81337C2.57331 8.05337 2.13331 7.29337 1.81331 6.54004C1.49331 5.78004 1.33331 5.05337 1.33331 4.36004C1.33331 3.90671 1.41331 3.47337 1.57331 3.07337C1.73331 2.66671 1.98665 2.29337 2.33998 1.96004C2.76665 1.54004 3.23331 1.33337 3.72665 1.33337C3.91331 1.33337 4.09998 1.37337 4.26665 1.45337C4.43998 1.53337 4.59331 1.65337 4.71331 1.82671L6.25998 4.00671C6.37998 4.17337 6.46665 4.32671 6.52665 4.47337C6.58665 4.61337 6.61998 4.75337 6.61998 4.88004C6.61998 5.04004 6.57331 5.20004 6.47998 5.35337C6.39331 5.50671 6.26665 5.66671 6.10665 5.82671L5.59998 6.35337C5.52665 6.42671 5.49331 6.51337 5.49331 6.62004C5.49331 6.67337 5.49998 6.72004 5.51331 6.77337C5.53331 6.82671 5.55331 6.86671 5.56665 6.90671C5.68665 7.12671 5.89331 7.41337 6.18665 7.76004C6.48665 8.10671 6.80665 8.46004 7.15331 8.81337C7.51331 9.16671 7.85998 9.49337 8.21331 9.79337C8.55998 10.0867 8.84665 10.2867 9.07331 10.4067C9.10665 10.42 9.14665 10.44 9.19331 10.46C9.24665 10.48 9.29998 10.4867 9.35998 10.4867C9.47331 10.4867 9.55998 10.4467 9.63331 10.3734L10.14 9.87337C10.3066 9.70671 10.4666 9.58004 10.62 9.50004C10.7733 9.40671 10.9266 9.36004 11.0933 9.36004C11.22 9.36004 11.3533 9.38671 11.5 9.44671C11.6466 9.50671 11.8 9.59337 11.9666 9.70671L14.1733 11.2734C14.3466 11.3934 14.4666 11.5334 14.54 11.7C14.6066 11.8667 14.6466 12.0334 14.6466 12.22Z'
            stroke='#43555A'
            strokeWidth='1.5'
            strokeMiterlimit='10'
          />
        </svg>
        <p className='text-sm text-[#43555A]'>{leadDetails.contact.cell_phone}</p>
      </div>
      <div className='flex items-center gap-x-2 my-2'>
        <svg
          width='16'
          height='16'
          viewBox='0 0 16 16'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M11.3333 13.6667H4.66665C2.66665 13.6667 1.33331 12.6667 1.33331 10.3334V5.66671C1.33331 3.33337 2.66665 2.33337 4.66665 2.33337H11.3333C13.3333 2.33337 14.6666 3.33337 14.6666 5.66671V10.3334C14.6666 12.6667 13.3333 13.6667 11.3333 13.6667Z'
            stroke='#43555A'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M11.3334 6.00003L9.24668 7.6667C8.56002 8.21336 7.43335 8.21336 6.74668 7.6667L4.66669 6.00003'
            stroke='#43555A'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </svg>
        <p className='text-sm text-[#43555A]'>{leadDetails.contact.email}</p>
      </div>
      <LeadRequestDetails leadDetails={leadDetails} />
    </>
  );
};

export default LeadAcceptedDetails;
