import { Lead } from '@/types/lead';

import dayjs from 'dayjs';

type Props = {
  leadDetails: Lead;
};
const LeadRequestDetails = ({ leadDetails }: Props) => {
  return (
    <>
      <p className='text-[#43555A] text-sm font-semibold'>Listing Address</p>
      <div className='flex items-center gap-x-2 mt-2'>
        <svg
          width='14'
          height='14'
          viewBox='0 0 14 14'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M7 7.83417C8.00516 7.83417 8.82 7.01933 8.82 6.01417C8.82 5.00901 8.00516 4.19417 7 4.19417C5.99484 4.19417 5.18 5.00901 5.18 6.01417C5.18 7.01933 5.99484 7.83417 7 7.83417Z'
            stroke='#4C737F'
            strokeWidth='1.5'
          />
          <path
            d='M2.11167 4.95251C3.26083 -0.0991602 10.745 -0.0933267 11.8883 4.95834C12.5592 7.92167 10.7158 10.43 9.1 11.9817C7.9275 13.1133 6.0725 13.1133 4.89417 11.9817C3.28417 10.43 1.44083 7.91584 2.11167 4.95251Z'
            stroke='#4C737F'
            strokeWidth='1.5'
          />
        </svg>
        <p className='text-sm font-medium text-[#6E7E83]'>
          {leadDetails?.listing_address ?? 'N/A'}
        </p>
      </div>
      <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
      <p className='text-[#43555A] text-sm font-semibold'>Arrival Date</p>
      <div className='flex items-center gap-x-2 mt-2'>
        <svg
          width='14'
          height='14'
          viewBox='0 0 14 14'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M4.66667 1.16663V2.91663'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M9.33333 1.16663V2.91663'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M2.04167 5.30249H11.9583'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M12.25 4.95829V9.91663C12.25 11.6666 11.375 12.8333 9.33333 12.8333H4.66667C2.625 12.8333 1.75 11.6666 1.75 9.91663V4.95829C1.75 3.20829 2.625 2.04163 4.66667 2.04163H9.33333C11.375 2.04163 12.25 3.20829 12.25 4.95829Z'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M6.99737 7.99162H7.00261'
            stroke='#4C737F'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M4.83835 7.99162H4.84359'
            stroke='#4C737F'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M4.83835 9.74162H4.84359'
            stroke='#4C737F'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </svg>
        <p className='text-sm font-medium text-[#6E7E83]'>
          {dayjs(leadDetails.arrival_date).isValid()
            ? dayjs(leadDetails.arrival_date).format('dddd, MMM D, YYYY')
            : 'N/A'}
        </p>
      </div>
      <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
      <p className='text-[#43555A] text-sm font-semibold'>Departure Date</p>
      <div className='flex items-center gap-x-2 mt-2'>
        <svg
          width='14'
          height='14'
          viewBox='0 0 14 14'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M4.66667 1.16663V2.91663'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M9.33333 1.16663V2.91663'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M2.04167 5.30249H11.9583'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M12.25 4.95829V9.91663C12.25 11.6666 11.375 12.8333 9.33333 12.8333H4.66667C2.625 12.8333 1.75 11.6666 1.75 9.91663V4.95829C1.75 3.20829 2.625 2.04163 4.66667 2.04163H9.33333C11.375 2.04163 12.25 3.20829 12.25 4.95829Z'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeMiterlimit='10'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M6.99737 7.99162H7.00261'
            stroke='#4C737F'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M4.83835 7.99162H4.84359'
            stroke='#4C737F'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M4.83835 9.74162H4.84359'
            stroke='#4C737F'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </svg>
        <p className='text-sm font-medium text-[#6E7E83]'>
          {dayjs(leadDetails.departure_date).isValid()
            ? dayjs(leadDetails.departure_date).format('dddd, MMM D, YYYY')
            : 'N/A'}
        </p>
      </div>
      <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
      <p className='text-[#43555A] text-sm font-semibold'>Guests</p>
      <div className='flex items-center gap-x-2 mt-2'>
        <svg
          width='14'
          height='14'
          viewBox='0 0 14 14'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M7 6.99996C8.61083 6.99996 9.91667 5.69412 9.91667 4.08329C9.91667 2.47246 8.61083 1.16663 7 1.16663C5.38917 1.16663 4.08333 2.47246 4.08333 4.08329C4.08333 5.69412 5.38917 6.99996 7 6.99996Z'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
          <path
            d='M12.0108 12.8333C12.0108 10.5758 9.765 8.75 7 8.75C4.235 8.75 1.98917 10.5758 1.98917 12.8333'
            stroke='#4C737F'
            strokeWidth='1.5'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </svg>
        <p className='text-sm font-medium text-[#6E7E83]'>
          {leadDetails.guest} adults, {leadDetails.children} children
        </p>
      </div>
      <hr className='my-2 border-[#D8E2E4] border-t-[1.5px]' />
      <p className='text-[#43555A] text-sm font-semibold'>Notes</p>
      <p className='text-sm font-medium text-[#6E7E83] mt-2 pl-6'>{leadDetails.note}</p>
    </>
  );
};

export default LeadRequestDetails;
