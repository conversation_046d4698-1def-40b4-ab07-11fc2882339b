'use client';

import { useState } from 'react';

import Button from '@/clients/ui/button';
import { IAgent } from '@/types/agents';

import dynamic from 'next/dynamic';

const ContactAgentDialog = dynamic(
  () => import('@/clients/components/nantucket-real-estate-agents/ContactAgentDialog'),
  {
    ssr: false,
  },
);

const ContactAgent = ({ agent }: { agent: IAgent }) => {
  const [showDialog, setShowDialog] = useState<boolean>(false);
  return (
    <>
      <Button
        tabIndex={0}
        onClick={() => setShowDialog(true)}
        className='w-full mt-[30px] md:mt-10'
        title={`Work with ${agent?.first_name} ${agent?.last_name}`}
      />
      {showDialog && (
        <ContactAgentDialog open={showDialog} onClose={() => setShowDialog(false)} agent={agent} />
      )}
    </>
  );
};

export default ContactAgent;
