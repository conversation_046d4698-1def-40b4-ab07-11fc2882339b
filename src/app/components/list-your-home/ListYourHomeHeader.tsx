import { ReactNode, Suspense } from 'react';

import BackgroundVideo from '@/clients/components/common/BackgroundVideo';

import Image from 'next/image';
import Link from 'next/link';

type Props = {
  children: ReactNode;
};

const ListYourHomeHeader = ({ children }: Props) => {
  return (
    <div className='relative w-full h-[calc(100vh-80px)] lg:h-[calc(100vh-88px)]'>
      <Image
        src='/images/list-your-home-bg.webp'
        alt='list your home background'
        width={0}
        height={0}
        sizes='100vw'
        className='w-full h-full object-cover md:hidden'
        priority
      />
      <BackgroundVideo playListUrl='https://res.cloudinary.com/dpf7sq6of/raw/upload/v1739256726/videos/list-your-home/ieciguicgrbvnabflqgh.m3u8n' />
      <div className='absolute inset-0 !bg-[rgba(0,0,0,0.3)]' />
      {children}
    </div>
  );
};

export default ListYourHomeHeader;
