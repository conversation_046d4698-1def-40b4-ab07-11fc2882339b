import parse from 'html-react-parser';

import { Icon } from '@/clients/components/icon';

import Image from 'next/image';
import Link from 'next/link';

type CardProps = {
  article: {
    image: string;
    date: string;
    author: string;
    title: string;
    text: string;
    slug: string;
  };
  preload?: boolean;
  className?: string;
};

export const BlogCard = ({ article, preload = false, className = '' }: CardProps) => {
  const decodedArticleText = parse(article.text);
  const decodedArticleTitle = parse(article.title);

  return (
    <div className={`relative shadow-md ${className}`}>
      {article.image && (
        <div className='relative mb-4 overflow-hidden h-52 md:h-44'>
          <Link href={article.slug}>
            <Image
              src={article.image}
              sizes='100vw'
              className='object-cover hover:scale-105'
              style={{
                width: '100%',
                height: '100%',
                transition: 'all .6s',
              }}
              priority={preload}
              width={330}
              height={205}
              alt={`blog image`}
            />
          </Link>
        </div>
      )}
      <div className='px-5 py-7'>
        <div className='flex justify-between items-center text-[#515967] text-sm'>
          <div className='flex'>
            <Icon icon='clock' />
            <div className='pl-2'>{article.date}</div>
          </div>
          <div className='flex'>
            <Icon icon='user' />
            <div className='pl-2'>{article.author}</div>
          </div>
        </div>
        <h2 className='font-semibold text-lg line-clamp-2 my-5'>
          <Link href={article.slug}>{decodedArticleTitle}</Link>
        </h2>
        <p className='line-clamp-3 text-sm'>{decodedArticleText}</p>
        <div className='flex mt-4'>
          <Link href={article.slug}>
            <button className='mr-2 text-sm font-semibold text-navy-blue'>Read More</button>
          </Link>
          <Icon height={24} width={17} icon='view-details' alt='View Details' />
        </div>
      </div>
    </div>
  );
};
