import { Icon } from '@/clients/components/icon';

import Link from 'next/link';

type RentalsPreviewProps = {
  children: React.ReactNode;
};

export const RentalsPreview = ({ children }: RentalsPreviewProps) => (
  <div className='bg-gradient-to-r from-slate-500 to-slate-400 text-white'>
    <div className='lg:pr-0 lg:container'>
      <div className='py-12 pl-4 md:pl-8 lg:py-16 lg:pl-0 lg:grid lg:grid-cols-2 lg:gap-x-10 lg:items-start'>
        <div className='flex flex-col gap-y-6 mb-8 max-w-[520px] xl:max-w-none xl:w-[610px] pr-4 lg:pr-0 lg:mb-0'>
          <h2 className='text-2xl font-bold lg:text-4xl lg:text-left'>
            From cottages to compounds, we have the vacation home for you!
          </h2>
          <div className='text-left mb-5'>
            There are plenty of luxurious vacation rentals available across the island that will
            help make your vacation unique. Whether you are looking for a cozy family getaway or a
            luxurious home that will transport you into another life, there is something available
            for everyone. Browse our rental listings today and find the dream property that will
            have you coming back to explore the island every chance you get.
          </div>
          <Link href='/nantucket-rentals'>
            <button className='py-2.5 px-7 w-72 box-border text-sm font-bold rounded bg-white text-[#1C4A5E] h-[52px]'>
              <span className='flex gap-x-1 items-center justify-between'>
                Browse Vacation Rentals <Icon icon='arrow-right' height={19} width={19} />
              </span>
            </button>
          </Link>
        </div>
        <div className='flex flex-nowrap overflow-auto pb-4 lg:pb-8 lg:overflow-auto lg:pl-12 xl:w-[840px] no-scrollbar'>
          {children}
        </div>
      </div>
    </div>
  </div>
);
