import Link from 'next/link';

export const Banner = () => (
  <div className='h-[650px] flex justify-center items-center flex-col bg-no-repeat bg-medium-gray lg:bg-cover lg:bg-hero-desktop'>
    <h1 className='text-white text-2xl px-4 md:text-4xl md:px-18 max-w-[860px] xl:px-0 text-center pb-10'>
      Representing the best Nantucket real estate and rentals <b>since 1931</b>
    </h1>
    <div className='flex flex-col gap-y-4 justify-center items-center md:flex-row'>
      <div className='md:mr-4'>
        <Link href='/nantucket-real-estate/'>
          <button className='rounded border border-white text-white w-36 text-center py-4 hover:bg-white active:bg-white hover:text-cyan-900 hover:font-bold'>
            Buy
          </button>
        </Link>
      </div>
      <div className='md:mr-4'>
        <Link href='https://www.congdonandcoleman.com/sell-your-nantucket-real-estate/'>
          <button className='rounded border border-white text-white w-36 text-center py-4 hover:bg-white active:bg-white hover:text-cyan-900 hover:font-bold'>
            Sell
          </button>
        </Link>
      </div>
      <div className='md:mr-4'>
        <Link href='/nantucket-rentals/'>
          <button className='rounded border border-white text-white w-36 text-center py-4 hover:bg-white active:bg-white hover:text-cyan-900 hover:font-bold'>
            Rent
          </button>
        </Link>
      </div>
    </div>
  </div>
);
