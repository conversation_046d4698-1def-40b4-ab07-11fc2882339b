import Image from 'next/image';
import Link from 'next/link';

export default function MobileMenu() {
  const prefix = process.env.VERCEL_ENV === 'production' ? 'https://www' : 'https://dev';

  return (
    <div className='menu h-full w-80 bg-white px-4 py-10 text-base-content shadow-md'>
      <Link href='/' className='mb-5 flex cursor-pointer items-center justify-center'>
        <Image
          alt='CNC logo'
          src='/images/cc-logo.svg'
          width={232}
          height={48}
          priority
          className='h-[40px] w-auto md:h-[48px]'
        />
      </Link>
      <ul>
        <li className='my-2 w-full'>
          <Link
            className=' flex-grow font-medium leading-5 text-label'
            href='/nantucket-real-estate'
          >
            Buy
          </Link>
          <ul>
            <li className='my-2 w-full'>
              <Link
                className=' flex-grow font-medium leading-5 text-label'
                href='/nantucket-real-estate/exclusive-listings?offset=0&ordering=-info__ListPrice'
              >
                Exclusive Listings
              </Link>
            </li>
          </ul>
        </li>
        <li className='my-2 w-full'>
          <Link
            className='font-medium leading-5 text-label'
            href='/sell-your-nantucket-real-estate'
          >
            Sell
          </Link>
        </li>
        <li className='my-2 w-full'>
          <Link
            className=' font-medium leading-5 text-label'
            href='/nantucket-rentals?offset=0&ordering=priority,-calendar_updated_at'
          >
            Rent
          </Link>
        </li>
        <li className='my-2 w-full'>
          <Link className=' font-medium leading-5 text-label' href='/nantucket-real-estate-agents'>
            Agents
          </Link>
        </li>
        <li className='my-2 w-full'>
          <p className='font-medium leading-5 text-label'>Nantucket</p>
          <ul>
            <li>
              <a
                className=' flex-grow font-medium leading-5 text-label'
                href={`${prefix}.congdonandcoleman.com/nantucket-dining-guide`}
              >
                Dining Guide
              </a>
            </li>
            <li>
              <a
                className=' flex-grow font-medium leading-5 text-label'
                href={`${prefix}.congdonandcoleman.com/nantucket-neighborhoods`}
              >
                Neighborhoods
              </a>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  );
}
