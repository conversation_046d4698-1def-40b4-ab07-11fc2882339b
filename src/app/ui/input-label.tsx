import React from 'react';

import cn from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  error?: boolean;
  children?: React.ReactNode;
} & React.ButtonHTMLAttributes<HTMLDivElement>;

const InputLabel = ({ error, children, className }: Props) => {
  return (
    <div
      className={twMerge(
        cn('text-sm leading-[18px] mb-1.5', {
          'text-red-main': error,
          'text-grey-main': !error,
        }),
        className,
      )}
    >
      {children}
    </div>
  );
};

export default InputLabel;
