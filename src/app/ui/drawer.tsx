import MobileMenu from '@/app/ui/mobile-menu';

export default function Drawer({ children }: { children: React.ReactNode }) {
  return (
    <div className='drawer'>
      <input
        id='cnc_mobile_menu_drawer'
        type='checkbox'
        className='drawer-toggle'
        aria-labelledby='drawer_overlay drawer_trigger'
      />
      <div className='drawer-content overflow-hidden'>{children}</div>
      <div className='drawer-side z-50 '>
        <label
          htmlFor='cnc_mobile_menu_drawer'
          id='drawer_overlay'
          aria-hidden='true'
          className='drawer-overlay'
        />
        <MobileMenu />
      </div>
    </div>
  );
}
