import React from 'react';

import { ChevronDownIcon } from '@heroicons/react/20/solid';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  title?: string | React.ReactNode;
  className?: string;
  showChevron?: boolean;
  children?: React.ReactNode;
  labelClassName?: string;
  contentClassName?: string;
  isOpen?: boolean;
};

const Dropdown = ({
  title = '',
  className = '',
  labelClassName = '',
  contentClassName = '',
  showChevron = false,
  children,
  isOpen,
}: Props) => {
  return (
    <div className={twMerge('dropdown', className)}>
      <label
        tabIndex={0}
        className={twMerge('flex cursor-pointer font-medium leading-5 text-label', labelClassName)}
      >
        {title}
        {showChevron && (
          <>
            <ChevronDownIcon
              className={classNames(
                'ml-2 h-5 w-5 pt-0.5 transition-all duration-150 group-hover:rotate-180',
                isOpen && 'rotate-180',
              )}
            />
          </>
        )}
      </label>
      <ul
        tabIndex={0}
        className={twMerge('menu dropdown-content z-[1] bg-white p-2 shadow', contentClassName)}
      >
        {children}
      </ul>
    </div>
  );
};

export default Dropdown;
