import Image from 'next/image';
import Link from 'next/link';

const prefix = process.env.VERCEL_ENV === 'production' ? 'https://www' : 'https://dev';

type LinkGroupProps = {
  title: string;
  children: React.ReactNode;
};

const SocialIcons = () => (
  <div className='grid grid-cols-2 gap-x-4'>
    <a target='_blank' href='https://www.facebook.com/nantucketbrokers'>
      <Image alt='Facebook logo' src='/images/fb-icon.svg' width={40} height={40} />
    </a>

    <a target='_blank' href='https://www.instagram.com/nantucketbrokers'>
      <Image alt='Facebook logo' src='/images/ig-icon.svg' width={40} height={40} />
    </a>
  </div>
);

const LinkGroup = ({ title, children }: LinkGroupProps) => {
  return (
    <div>
      <h4 className='mb-5 text-xl font-bold md:mb-12'>{title}</h4>
      <ul className='flex flex-col gap-y-4 text-[#DBF5FF]'>{children}</ul>
    </div>
  );
};

const Footer = () => {
  return (
    <footer className='bg-navy-blue px-10 py-12 text-white md:px-16 md:py-14 lg:px-32 lg:py-28'>
      <div className='container'>
        <div className='md:grid md:grid-cols-2 md:gap-x-14'>
          <div>
            <div className='mb-12 flex items-center justify-center md:justify-start'>
              <a
                href={`${prefix}.congdonandcoleman.com/nantucket-real-estate/`}
                className='cursor-pointer'
              >
                <Image alt='CNC logo' src='/images/cc-logo-light.svg' width={254} height={55} />
              </a>
            </div>
            <address className='flex flex-col gap-y-5 px-4 text-sm'>
              <p>
                57 Main Street, Second Floor
                <br />
                Nantucket, MA 02554
              </p>
              <a className='hover:underline' href='mailto:<EMAIL>'>
                <EMAIL>
              </a>
              <p>
                <a href='tel:(508)325-5000'>(*************</a>
              </p>
            </address>
          </div>
          <div className='mt-9 flex justify-between md:mt-0 md:justify-evenly'>
            <LinkGroup title='Quick Links'>
              <li>
                <a
                  className='hover:underline'
                  href={`${prefix}.congdonandcoleman.com/nantucket-real-estate/`}
                >
                  Buy
                </a>
              </li>
              <li>
                <a
                  className='hover:underline'
                  href={`${prefix}.congdonandcoleman.com/sell-your-nantucket-real-estate`}
                >
                  Sell
                </a>
              </li>
              <li>
                <a
                  className='hover:underline'
                  href={`${prefix}.congdonandcoleman.com/nantucket-rentals/`}
                >
                  Rent
                </a>
              </li>
              <li>
                <a
                  className='hover:underline'
                  href={`${prefix}.congdonandcoleman.com/nantucket-real-estate-agents`}
                >
                  Agents
                </a>
              </li>
              <li>
                <Link className='hover:underline' href='/blog'>
                  C&C Blog
                </Link>
              </li>
            </LinkGroup>

            <div className='flex flex-col justify-between'>
              <LinkGroup title='About Us'>
                <li>
                  <a
                    className='hover:underline'
                    href='https://www.congdonandcoleman.com/nantucket-copyright'
                  >
                    Copyright
                  </a>
                </li>
                <li>
                  <a
                    className='hover:underline'
                    href='https://www.congdonandcoleman.com/nantucket-privacy'
                  >
                    Privacy Policy
                  </a>
                </li>
              </LinkGroup>

              <div className='md:hidden'>
                <SocialIcons />
              </div>
            </div>
          </div>
        </div>
        <div>
          <hr className='my-8 h-px bg-[#E5F1F4] md:mt-20' />
          <div className='items-center justify-between md:flex'>
            <div>
              <p className='flex flex-col gap-y-2 text-sm text-[#DBF5FF] md:flex-row md:gap-x-2'>
                <span>Copyright © 2023</span>
                <span>Congdon & Coleman Real Estate.</span>
                <span>All rights reserved.</span>
              </p>
            </div>
            <div className='hidden md:block'>
              <SocialIcons />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
