import { Bars3Icon, PhoneIcon } from '@heroicons/react/24/outline';

import Dropdown from '@/app/ui/dropdown';

import Image from 'next/image';
import Link from 'next/link';

const Header = () => {
  const prefix = process.env.VERCEL_ENV === 'production' ? 'https://www' : 'https://dev';

  return (
    <div className='fixed z-10 flex w-full items-center bg-white shadow-header md:top-0'>
      <div className='container flex flex-row items-center justify-between py-5 text-sm'>
        <label htmlFor='cnc_mobile_menu_drawer' id='drawer_trigger' className='md:hidden'>
          <Bars3Icon className='h-6 w-auto text-black-3' />
        </label>
        <Link href='/' className='cursor-pointer '>
          <Image
            alt='CNC logo'
            src='/images/cc-logo.svg'
            width={232}
            height={48}
            priority
            className='h-[40px] w-auto md:h-[48px]'
          />
        </Link>
        <div className='hidden items-center justify-end md:flex'>
          <Dropdown
            title='Buy'
            className='mr-[50px] w-max group dropdown-hover'
            contentClassName='w-52'
            showChevron
          >
            <ul>
              <li>
                <Link
                  className='rounded-none'
                  href='/nantucket-real-estate/?offset=0&ordering=-info__ListPrice'
                >
                  Exclusive Listings
                </Link>
              </li>
            </ul>
          </Dropdown>
          <Link
            className='mr-[50px] font-medium leading-5 text-label'
            href='/sell-your-nantucket-real-estate'
          >
            Sell
          </Link>
          <Link
            className='mr-[50px] font-medium leading-5 text-label'
            href='/nantucket-rentals?offset=0&ordering=priority,-calendar_updated_at'
          >
            Rent
          </Link>
          <Link
            className='mr-[50px] font-medium leading-5 text-label'
            href='/nantucket-real-estate-agents'
          >
            Agents
          </Link>

          <Dropdown
            title='Nantucket'
            className='mr-[50px] w-max group dropdown-hover'
            contentClassName='w-52'
            showChevron
          >
            <ul>
              <li>
                <a
                  className='rounded-none'
                  href={`${prefix}.congdonandcoleman.com/nantucket-dining-guide`}
                >
                  Dining Guide
                </a>
              </li>
              <li>
                <a
                  className='rounded-none'
                  href={`${prefix}.congdonandcoleman.com/nantucket-neighborhoods`}
                >
                  Neighborhoods
                </a>
              </li>
            </ul>
          </Dropdown>
        </div>
        <a href='tel:5083255000' className='block md:hidden' aria-label='5 0 8. 3 2 5. 5 0 0 0.'>
          <PhoneIcon className='h-6 w-auto text-black-3' />
        </a>
      </div>
    </div>
  );
};

export default Header;
