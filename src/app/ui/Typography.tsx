import { twMerge } from 'tailwind-merge';

export const H1 = (props: React.HTMLAttributes<HTMLHeadingElement>) => {
  return (
    <h1
      {...props}
      className={twMerge(
        'text-[24px] md:text-[28px] leading-[175%] tracking-[0.5px] text-primary-text',
        props.className,
      )}
    >
      {props.children}
    </h1>
  );
};

export const H2 = (props: React.HTMLAttributes<HTMLHeadingElement>) => {
  return (
    <h2
      {...props}
      className={twMerge(
        'text-[20px] md:text-[24px] leading-[140%] tracking-[0.5px] font-medium text-primary-text',
        props.className,
      )}
    >
      {props.children}
    </h2>
  );
};

export const H4 = (props: React.HTMLAttributes<HTMLHeadingElement>) => {
  return <H1 className={twMerge('font-medium text-lg', props.className)}>{props.children}</H1>;
};

export const P = (props: React.HTMLAttributes<HTMLParagraphElement>) => {
  return <p {...props} className={twMerge('text-sm m-0', props.className)} />;
};

export const P1 = (props: React.HTMLAttributes<HTMLParagraphElement>) => {
  return (
    <p
      {...props}
      className={twMerge(
        'text-sm leading-[130%] text-primary-text tracking-[0.5px] m-0',
        props.className,
      )}
    />
  );
};

export const P2 = (props: React.HTMLAttributes<HTMLParagraphElement>) => {
  return (
    <P1 className={twMerge('font-medium text-xs leading-[150%]', props.className)}>
      {props.children}
    </P1>
  );
};
