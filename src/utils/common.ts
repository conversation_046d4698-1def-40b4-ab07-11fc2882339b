export const currencyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export const currencyFormatterRound = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 0,
  minimumFractionDigits: 0,
});

export const formatCurrency = (value: number) => {
  if (value % 1 == 0) {
    return currencyFormatterRound.format(value);
  } else {
    return currencyFormatter.format(value);
  }
};

export const isBrowser = typeof window !== 'undefined';

export const isBotUserAgent = () => {
  const agent = window.navigator.userAgent;

  const botUserAgentsArray = [
    'bot',
    'googlebot',
    'bingbot',
    'linkedinbot',
    'mediapartners-google',
    'lighthouse',
    'insights',
  ];
  return botUserAgentsArray.some((_bot) => agent.toLowerCase().includes(_bot));
};

export const roundInteger = (num: number) => {
  const hasdecimal = num % 1 != 0;
  return hasdecimal ? num.toFixed(1) : num;
};
