import { TOTAL_SLOTS_IN_CALENDAR } from '@/clients/components/calendar';
import { IListingAvailability, IListingRate } from '@/types/rental-listing-details';

import dayjs, { Dayjs } from 'dayjs';

export const getRatesForCalendar = (
  rateRanges: IListingRate[],
  year: number,
  month: number,
  numberOfDaysLastMonth: number,
  numberOfDaysInMonth: number,
  firstDayOfMonth: number,
) =>
  rateRanges?.filter((_rate) => {
    const isBeforeStartDateOfCalendar = dayjs(_rate.to_date).isBefore(
      dayjs(`${year}/${month}/${numberOfDaysLastMonth - firstDayOfMonth + 1}`, `YYYY/M/D`),
    );

    const isAfterLastDateOfCalendar = dayjs(_rate.from_date).isAfter(
      dayjs(
        `${year}/${month + 2}/${
          (TOTAL_SLOTS_IN_CALENDAR - firstDayOfMonth - numberOfDaysInMonth) % 7
        }`,
        `YYYY/M/D`,
      ),
    );

    return !isBeforeStartDateOfCalendar && !isAfterLastDateOfCalendar;
  });

export const getAvailabitiesForCalendar = (
  availableRanges: IListingAvailability[],
  year: number,
  month: number,
  numberOfDaysLastMonth: number,
  numberOfDaysInMonth: number,
  firstDayOfMonth: number,
) =>
  availableRanges?.filter((_availability) => {
    const isBeforeStartDateOfCalendar = dayjs(_availability.to_date).isBefore(
      dayjs(`${year}/${month}/${numberOfDaysLastMonth - firstDayOfMonth + 1}`, `YYYY/M/D`),
    );

    const isAfterLastDateOfCalendar = dayjs(_availability.from_date).isAfter(
      dayjs(
        `${year}/${month + 2}/${
          (TOTAL_SLOTS_IN_CALENDAR - firstDayOfMonth - numberOfDaysInMonth) % 7
        }`,
        `YYYY/M/D`,
      ),
    );

    return !isBeforeStartDateOfCalendar && !isAfterLastDateOfCalendar;
  });

export const formatAvailabilityDateString = (date?: string): Dayjs => dayjs(date, 'YYYY-MM-DD');

export const rangesOverlap = (
  range1: (Date | null | undefined | Dayjs)[],
  range2: (Date | null | undefined | Dayjs)[],
  excludeStartAndEnd?: boolean,
): boolean => {
  if (!range1[0] || !range1[1] || !range2[0] || !range2[1]) {
    return false;
  }

  if (excludeStartAndEnd) {
    return range1[0] < range2[1] && range2[0] < range1[1];
  }
  return range1[0] <= range2[1] && range2[0] <= range1[1];
};

export const getDatesBetweenDates = (startDate?: Dayjs | null, endDate?: Dayjs | null) => {
  const daysArray: Dayjs[] = [];
  if (!startDate || !endDate) {
    return daysArray;
  }
  const numberOfDays = endDate.startOf('day').diff(startDate.endOf('day'), 'day');

  for (let i = 1; i <= numberOfDays; i++) {
    daysArray.push(startDate.add(1 * i, 'day'));
  }

  return daysArray;
};
