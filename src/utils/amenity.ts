import { IListingDetails, ListingItemWithId } from '@/types/rental-listing-details';

export const getAmenityTitle = (key: string) =>
  key
    .split('_')
    .join(' ')
    .replace(/\b\w/g, (l) => l.toUpperCase());

export function getAmenityIconSrcPath(amenityKey: string) {
  switch (amenityKey) {
    case 'air_conditioning':
      return '/images/icons/amenity-icons/ic_ac.svg';
    case 'bbq_tool':
      return '/images/icons/amenity-icons/ic_BBQUtensil.svg';
    case 'beach_chair':
      return '/images/icons/amenity-icons/ic_BeachChair.svg';
    case 'beach_towel':
      return '/images/icons/amenity-icons/ic_BeachTowel.svg';
    case 'beach_umbrella':
      return '/images/icons/amenity-icons/ic_BeachUmbrella.svg';
    case 'bicycle':
      return '/images/icons/amenity-icons/ic_Bicycle.svg';
    case 'blender':
      return '/images/icons/amenity-icons/ic_Blender.svg';
    case 'coffee_maker':
      return '/images/icons/amenity-icons/ic_CoffeeMachine.svg';
    case 'cooler':
      return '/images/icons/amenity-icons/ic_Cooler.svg';
    case 'deck':
      return '/images/icons/amenity-icons/ic_Deck.svg';
    case 'dish_or_cup_or_utensil':
      return '/images/icons/amenity-icons/ic_DishUtensil.svg';
    case 'dishwasher':
      return '/images/icons/amenity-icons/ic_Dishwasher.svg';
    case 'dining_area':
    case 'dining_room':
    case 'outdoor_dining_area':
      return '/images/icons/amenity-icons/ic_DiningArea.svg';
    case 'dryer':
      return '/images/icons/amenity-icons/ic_Dryer.svg';
    case 'fan':
      return '/images/icons/amenity-icons/ic_fan.svg';
    case 'fireplace':
      return '/images/icons/amenity-icons/ic_fireplace.svg';
    case 'food_processor':
      return '/images/icons/amenity-icons/ic_food-processor.svg';
    case 'freezer':
      return '/images/icons/amenity-icons/ic_Freezer.svg';
    case 'grill':
      return '/images/icons/amenity-icons/ic_Grill.svg';
    case 'gym':
      return '/images/icons/amenity-icons/ic_gym.svg';
    case 'hair_dryer':
      return '/images/icons/amenity-icons/ic_HairDryer.svg';
    case 'heating':
      return '/images/icons/amenity-icons/ic_Heat.svg';
    case 'ice_maker':
      return '/images/icons/amenity-icons/ic_IceMaker.svg';
    case 'ice_tray':
      return '/images/icons/amenity-icons/ic_ice-tray.svg';
    case 'ironing_board':
      return '/images/icons/amenity-icons/ic_IronBoard.svg';
    case 'linen':
      return '/images/icons/amenity-icons/ic_LinensProvided.svg';
    case 'lobster_pot':
      return '/images/icons/amenity-icons/ic_LobsterPot.svg';
    case 'microwave':
      return '/images/icons/amenity-icons/ic_Microwave.svg';
    case 'outdoor_shower':
      return '/images/icons/amenity-icons/ic_OutdoorShower.svg';
    case 'oven':
      return '/images/icons/amenity-icons/ic_Oven.svg';
    case 'towel':
      return '/images/icons/amenity-icons/ic_PaperTowels.svg';
    case 'parking':
      return '/images/icons/amenity-icons/ic_Parking.svg';
    case 'patio':
      return '/images/icons/amenity-icons/ic_Patio.svg';
    case 'porch':
      return '/images/icons/amenity-icons/ic_Porch.svg';
    case 'pot_or_pan':
      return '/images/icons/amenity-icons/ic_PotPan.svg';
    case 'pool':
      return '/images/icons/amenity-icons/ic_SwimingPool.svg';
    case 'refrigerator':
      return '/images/icons/amenity-icons/ic_Refrigederator.svg';
    case 'seating':
      return '/images/icons/amenity-icons/ic_DiningSeat.svg';
    case 'shower':
      return '/images/icons/amenity-icons/ic_shower.svg';
    case 'soaps':
      return '/images/icons/amenity-icons/ic_Soap.svg';
    case 'sonos':
      return '/images/icons/amenity-icons/ic_Sonos.svg';
    case 'stove':
      return '/images/icons/amenity-icons/ic_Stove.svg';
    case 'sleeping_capacity':
      return '/images/icons/amenity-icons/ic_Capacity.svg';
    case 'tableware':
      return '/images/icons/amenity-icons/ic_Tableware.svg';
    case 'tea_kettle':
      return '/images/icons/amenity-icons/ic_teaKettle.svg';
    case 'tennis_court':
      return '/images/icons/amenity-icons/ic_TennisCourt.svg';
    case 'tv':
      return '/images/icons/amenity-icons/ic_tivi.svg';
    case 'toaster':
      return '/images/icons/amenity-icons/ic_Toaster.svg';
    case 'toaster_oven':
      return '/images/icons/amenity-icons/ic_ToasterOven.svg';
    case 'toilets':
      return '/images/icons/amenity-icons/ic_toilets.svg';
    case 'toilet_paper':
      return '/images/icons/amenity-icons/ic_ToiletPaper.svg';
    case 'towels':
      return '/images/icons/amenity-icons/ic_Towels.svg';
    case 'hot_tub_or_spa':
      return '/images/icons/amenity-icons/ic_tub.svg';
    case 'wifi':
      return '/images/icons/amenity-icons/ic_wifi.svg';
    case 'wine_glasses':
      return '/images/icons/amenity-icons/ic_WineGlasses.svg';
    case 'yard':
      return '/images/icons/amenity-icons/ic_Yard.svg';

    default:
      return '/images/icons/amenity-icons/ic_Porch.svg';
  }
}

export const getAmenityQuantity = (key: string, property: IListingDetails) => {
  return property[`${key}_quantity` as unknown as keyof IListingDetails] ?? 0;
};

export const getAmenityType = (key: string, property: IListingDetails) => {
  return (
    (property[`${key}_type` as unknown as keyof IListingDetails] as ListingItemWithId)?.name ?? null
  );
};
