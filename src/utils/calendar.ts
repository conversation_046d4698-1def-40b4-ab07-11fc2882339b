import { IListingRate } from '@/types/rental-listing-details';

import dayjs, { Dayjs } from 'dayjs';

export type MonthList = {
  month: number;
  year: number;
}[];

export const getNumberOfDaysInMonth = (year: number, month: number): number => {
  return dayjs(`${year}-${month + 1}`, 'YYYY-M').daysInMonth();
};

export const getFirstDayOfMonth = (year: number, month: number) => {
  return dayjs(`${year}-${month + 1}`, 'YYYY-M')
    .startOf('month')
    .day();
};

export const getPropertyDetailsCalendarMonths = (): MonthList => {
  const currentMonth = dayjs();

  // check if current date is on or after September 1
  if (currentMonth.isSameOrAfter(dayjs(`${currentMonth.year()}-09-01`, 'YYYY-MM-DD'))) {
    return getMonthsBetweenDates(
      // currentMonth.startOf('year').add(1, 'years').add(4, 'months').format('YYYY-MM-DD'),
      currentMonth.format('YYYY-MM-DD'),
      currentMonth.startOf('year').add(1, 'years').add(11, 'months').format('YYYY-MM-DD'),
    );
  }

  // check if current date is before June 1
  if (currentMonth.isBefore(dayjs(`${currentMonth.year()}-05-01`, 'YYYY-MM-DD'))) {
    return getMonthsBetweenDates(
      // currentMonth.startOf('year').add(4, 'months').format('YYYY-MM-DD'),
      currentMonth.format('YYYY-MM-DD'),
      currentMonth.startOf('year').add(1, 'years').add(11, 'months').format('YYYY-MM-DD'),
    );
  }

  return getMonthsBetweenDates(
    currentMonth.format('YYYY-MM-DD'),
    currentMonth.startOf('year').add(1, 'years').add(11, 'months').format('YYYY-MM-DD'),
  );
};

export const getMonthsBetweenDates = (startDate?: string, endDate?: string) => {
  const months: MonthList = [];
  if (!startDate || !endDate) {
    return months;
  }
  const start = dayjs(startDate, 'YYYY-MM-DD').startOf('month');
  const end = dayjs(endDate, 'YYYY-MM-DD').endOf('month');
  const numberOfMonths = end.diff(start, 'month');

  const current = start;
  for (let i = 0; i <= numberOfMonths; i++) {
    months.push({
      year: current.add(i, 'month').year(),
      month: current.add(i, 'month').month(),
    });
  }

  return months;
};

export const isBetweenTwoDates = (
  startDate: Dayjs | string | null,
  endDate: Dayjs | string | null,
  date: Dayjs,
  excludeStartAndEnd?: boolean,
): boolean => {
  const start = dayjs(startDate, 'YYYY-MM-DD').startOf('day');
  const end = dayjs(endDate, 'YYYY-MM-DD').endOf('day');
  if (excludeStartAndEnd) {
    return date.isBetween(start, end);
  }
  return date.isBetween(start, end) || date.isSame(start) || date.isSame(end);
};

export const getRentalStartMonth = (): Date => {
  const currentMonth = dayjs();

  // check if current date is on or after September 1
  if (currentMonth.isSameOrAfter(dayjs(`${currentMonth.year()}-09-01`, 'YYYY-MM-DD'))) {
    return currentMonth.startOf('year').add(1, 'years').add(4, 'months').toDate();
  }

  // check if current date is before June 1
  if (currentMonth.isBefore(dayjs(`${currentMonth.year()}-05-01`, 'YYYY-MM-DD'))) {
    return currentMonth.startOf('year').add(4, 'months').toDate();
  }

  return dayjs().toDate();
};

export const checkIfDateHasRate = (date: Dayjs, rentalRates: IListingRate[] = []): boolean => {
  return (
    rentalRates.filter(({ from_date, to_date }) => isBetweenTwoDates(from_date, to_date, date))
      .length > 0
  );
};

export const getSelectedAvailabilityMonthnumber = (monthList: MonthList) => {
  const currentMonth = dayjs();
  // check if current date is on or after September 1
  if (currentMonth.isSameOrAfter(dayjs(`${currentMonth.year()}-09-01`, 'YYYY-MM-DD'))) {
    return monthList.findIndex(
      (_month) =>
        _month.month === currentMonth.startOf('year').add(1, 'years').add(4, 'months').get('month'),
    );
  }

  // check if current date is before June 1
  if (currentMonth.isBefore(dayjs(`${currentMonth.year()}-05-01`, 'YYYY-MM-DD'))) {
    return monthList.findIndex(
      (_month) => _month.month === currentMonth.startOf('year').add(4, 'months').get('month'),
    );
  }

  return monthList.findIndex((_month) => _month.month === currentMonth.get('month'));
};
