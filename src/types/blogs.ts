export interface Blog {
  id: number;
  author: number;
  categories: number[];
  comment_status: string;
  content: {
    rendered: string;
  };
  date: string;
  date_gmt: string;
  excerpt?: {
    rendered: string;
  };
  meta: any[];
  slug: string;
  tags: any[];
  link: string;
  title: {
    rendered: string;
  };
  yoast_head: string;
  yoast_head_json: { [x: string]: string };
  _embedded: {
    author: { id: number; name?: string; avatar_urls?: any }[];
    'wp:featuredmedia': {
      id: number;
      caption?: {
        rendered: string;
      };
      source_url: string;
    }[];
  };
}
