export enum ProgressStatus {
  LOADING = 'LOADING',
  UPDATING = 'UPDATING',
  DELETING = 'DELETING',
  SUCCESSFUL = 'SUCCESSFUL',
  FAILED = 'FAILED',
}

export type IPagination = {
  count?: number;
  currentPage?: number;
  next?: any;
  previous?: any;
};

export type Nullable<T> = T | null;

export enum TopFilterType {
  RENTALS = 'RENTALS',
  REAL_ESTATE = 'REAL_ESTATE',
}

export type EntityID = string | number;
