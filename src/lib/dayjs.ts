import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';

/**
 * Configure dayjs with required plugins
 * This should be imported once at the application entry point
 */
export const configureDayjs = () => {
  dayjs.extend(localeData);
  dayjs.extend(isSameOrBefore);
  dayjs.extend(isSameOrAfter);
  dayjs.extend(isBetween);
  dayjs().localeData();
};

// Auto-configure when this module is imported
configureDayjs();

export default dayjs;
