/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/clients/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',
          xs: '1rem',
          sm: '1.5rem',
        },
        screens: {
          sm: '600px',
          md: '728px',
          lg: '1024px',
          xl: '1200px',
          '2xl': '1200px',
        },
      },
      colors: {
        label: '#43555A',
        'default-font': '#09182C',
        'primary-slate': '#678993',
        'light-slate': '#d9eaf0',
        'navy-blue': '#1C4A5E',
        'blue-main': '#15A5E5',
        'gray-main': '#6D7380',
        'red-main': '#EB1648',
        'light-gray': '#F5F9FF',
        'medium-gray': '#6D8994',
        'black-3': '#6E7E83',
        olive: '#4C737F',
        'olive-variant': '#5E7B87',
        disabled: '#DEE1EB',
        primary: '#E5F1F4',
        'gray-80': '#5E6774',
        'dark-blue': '#074059',
        'dark-3': '#6E7E83',
        'dark-4': '#B1C4C9',
      },
      borderColor: {
        DEFAULT: '#DEE1EB',
      },
      fontFamily: {
        poppins: 'var(--font-poppins)',
      },
      boxShadow: {
        header: '0 5px 10px 0 rgb(17 61 71 / 5%)',
        card: '0px 4px 12px 0px rgba(13, 99, 137, 0.25)',
      },
      backgroundImage: {
        'hero-mobile':
          'linear-gradient(270deg, rgba(9, 42, 53, 0.07) 0%, rgba(9, 42, 53, 0.81) 100%),url("/images/home-mobile.webp")',
        'hero-desktop':
          'linear-gradient(270deg, rgba(9, 42, 53, 0.07) 0%, rgba(9, 42, 53, 0.81) 100%),url("/images/home.webp")',
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      backgroundColor: {
        backdrop: '#0E1717',
      },
    },
  },
  daisyui: {
    themes: ['light'],
  },
  plugins: [require('daisyui')],
};
